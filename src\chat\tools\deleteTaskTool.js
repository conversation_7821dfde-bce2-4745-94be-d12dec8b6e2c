import { DynamicTool } from '@langchain/core/tools';

import { parseTaskDescription } from './parseInput.js';

/**
 * Create a LangChain DynamicTool to delete a task by description.
 * Single responsibility: construct and return the delete_task tool.
 *
 * @param {TaskManager} taskManager
 * @param {string|number} userId
 * @returns {DynamicTool}
 */
export function deleteTaskTool(taskManager, userId) {
	return new DynamicTool({
		name: 'delete_task',
		description:
			'Use this tool to permanently delete a task when the user no longer needs it. Provide a description or partial description of the task to delete.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot delete task.';
				}

				const taskDescription = parseTaskDescription(input);
				if (!taskDescription) {
					return 'Error: Task description is required to delete a task. Please specify which task you want to remove.';
				}

				const deleted = await taskManager.deleteTaskByDescription(userId, taskDescription);

				if (deleted) {
					console.log(`delete_task: Task deleted for user ${userId}: ${taskDescription}`);
					return `🗑️ Task matching "${taskDescription}" has been deleted from your list.`;
				} else {
					return `I couldn't find a pending task matching "${taskDescription}". Use the list_tasks tool to see your current tasks.`;
				}
			} catch (error) {
				console.error('Error in delete_task tool:', error);
				return 'Error: Failed to delete the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description: 'The description or partial description of the task to delete.',
				},
			},
			required: ['description'],
		},
	});
}
