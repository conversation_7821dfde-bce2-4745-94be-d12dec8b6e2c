import { handleClearHistoryCommand } from './handlers/clearHistory.js';
import { handleCompleteTaskCommand } from './handlers/completeTask.js';
import { handleDeleteTaskCommand } from './handlers/deleteTask.js';
import { handleListTasksCommand } from './handlers/listTasks.js';

/**
 * Processes a detected command by routing it to the appropriate handler function.
 *
 * This function acts as the central command router, mapping command types defined
 * in the command configuration to their corresponding handler functions. It follows
 * the Open/Closed Principle by allowing new commands to be added through configuration
 * changes without modifying the routing logic.
 *
 * The router provides:
 * - Centralized command dispatch with consistent error handling
 * - Support for command-specific argument parsing requirements
 * - Logging and monitoring of command execution
 * - Graceful handling of unknown command types
 *
 * @module commands/commandRouter
 * @param {string} commandType - The type of command to process (e.g., 'CLEAR_HISTORY')
 * @param {Object} env - Environment variables containing API keys and configuration
 * @param {Object} messageData - Message metadata containing chat ID, user ID, and message details
 * @param {string} botUsername - The bot's username used for context and validation
 * @param {string} text - The full command text including any arguments for parsing
 * @returns {Promise<boolean>} True if the command was processed successfully, false otherwise
 *
 * @example
 * // Route a clear history command
 * const success = await processCommand('CLEAR_HISTORY', env, messageData, 'mybot', '/clear');
 * console.log(success); // true if history cleared successfully
 *
 * @example
 * // Route a task command with arguments
 * const success = await processCommand('COMPLETE_TASK', env, messageData, 'mybot', '/done task123');
 * console.log(success); // true if task completed successfully
 *
 * @example
 * // Handle unknown command type
 * const success = await processCommand('UNKNOWN_COMMAND', env, messageData, 'mybot', '/unknown');
 * console.log(success); // false - unknown command type
 *
 * @throws {Error} If command handler encounters an unexpected error
 */
export async function processCommand(commandType, env, messageData, botUsername, text) {
	const { chatId, userId } = messageData;

	// Route the command to the appropriate handler function
	switch (commandType) {
		case 'CLEAR_HISTORY':
			return await handleClearHistoryCommand(env, chatId, userId, botUsername);
		case 'LIST_TASKS':
			return await handleListTasksCommand(env, chatId, userId);
		case 'COMPLETE_TASK':
			return await handleCompleteTaskCommand(env, chatId, userId, text);
		case 'DELETE_TASK':
			return await handleDeleteTaskCommand(env, chatId, userId, text);
		default:
			console.warn(`Unknown command type: ${commandType}`);
			return false;
	}
}
