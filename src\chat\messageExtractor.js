import { checkBotMention } from './telegramUtils.js';

/**
 * Extracts and normalizes relevant data from the Telegram webhook payload.
 * This function processes both direct messages and callback queries, handling
 * various content types including text, photos, documents, and media groups.
 *
 * The extraction process includes:
 * - Message content normalization (text, captions, media)
 * - User and chat metadata extraction
 * - Bot mention removal from text content
 * - Support for reply-to message contexts
 * - Media group handling for albums
 *
 * @module chat/messageExtractor
 * @param {Object} env - Environment variables containing bot configuration and TELEGRAM_BOT_USERNAME
 * @param {Object} webhookData - Raw Telegram webhook payload containing message or callback_query
 * @returns {Object} Normalized message data with consistent structure containing:
 * @returns {Object} return.message - Original Telegram message object
 * @returns {number|string} return.chatId - Telegram chat identifier
 * @returns {string} return.text - Cleaned message text content (bot mentions removed)
 * @returns {Array} return.photo - Array of photo objects if message contains photos
 * @returns {Object} return.document - Document object if message contains a document
 * @returns {number} return.userId - Telegram user identifier
 * @returns {string} return.username - Telegram username (or 'unknown' if not available)
 * @returns {string} return.firstName - User's first name
 * @returns {string} return.lastName - User's last name
 * @returns {number} return.messageId - Unique message identifier
 * @returns {number} return.messageDate - Unix timestamp of message creation
 * @returns {string} return.chatType - Type of chat ('private', 'group', 'supergroup', 'channel')
 * @returns {string} return.mediaGroupId - Identifier for grouped media messages
 * @returns {Object} return.replyToMessage - Original message object that was replied to
 * @returns {Array} return.replyToPhoto - Photo objects from replied-to message
 * @returns {Object} return.replyToDocument - Document object from replied-to message
 *
 * @example
 * // Extract data from a text message
 * const messageData = extractMessageData(env, webhookData);
 * console.log(messageData.text); // Cleaned message text
 * console.log(messageData.userId); // Sender's Telegram ID
 *
 * @example
 * // Extract data from a photo with caption
 * const messageData = extractMessageData(env, webhookDataWithPhoto);
 * console.log(messageData.text); // Photo caption
 * console.log(messageData.photo.length); // Number of photo variants
 *
 * @throws {Error} If webhook data structure is malformed or missing required fields
 */
export function extractMessageData(env, webhookData) {
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	const chatId = message?.chat?.id;
	let text = message?.text || '';
	const photo = message?.photo || [];
	const document = message?.document || {};
	const replyToMessage = message?.reply_to_message;
	const replyToPhoto = replyToMessage?.photo || [];
	const replyToDocument = replyToMessage?.document || {};

	// For media messages, use the caption as the text content
	// For text-only messages, use the text directly
	if (photo.length > 0 || document.file_id) {
		text = message?.caption || '';
	}

	// Remove bot username mentions from the text to focus on actual content
	const botUsername = env.TELEGRAM_BOT_USERNAME;
	const mentionRegex = new RegExp(`@${botUsername}\\b`, 'gi');
	text = text.replace(mentionRegex, '').trim();

	const userId = message?.from?.id;
	const username = message?.from?.username || 'unknown';
	const firstName = message?.from?.first_name || '';
	const lastName = message?.from?.last_name || '';
	const messageId = message?.message_id;
	const messageDate = message?.date;
	const chatType = message?.chat?.type;
	const mediaGroupId = message?.media_group_id;

	return {
		message, // Original message object for downstream processing
		chatId, // Telegram chat identifier
		text, // Message text content (cleaned of bot mentions)
		photo, // Array of photo objects (if any)
		document, // Document object (if any)
		userId, // Telegram user identifier
		username, // Telegram username (or 'unknown')
		firstName, // User's first name
		lastName, // User's last name
		messageId, // Unique message identifier
		messageDate, // Unix timestamp of message
		chatType, // Type of chat ('private', 'group', 'supergroup', etc.)
		mediaGroupId, // Identifier for grouped media (if applicable)
		replyToMessage, // Original message object that was replied to
		replyToPhoto, // Photo in replied-to message (if any)
		replyToDocument, // Document in replied-to message (if any)
	};
}

/**
 * Determines whether an incoming message should be processed by the bot.
 * Implements comprehensive business logic for message filtering based on:
 * - Content type (text, photos, documents, media groups)
 * - Chat context (private vs. group chats)
 * - Bot mention requirements for group messages
 * - Media content validation
 *
 * Processing rules:
 * - Media messages (photos/documents) are always processed, with or without captions
 * - Private chat messages are always processed if they contain content
 * - Group chat text messages require explicit bot mentions
 * - Media group messages without captions are skipped
 * - Messages without any processable content are skipped
 *
 * @module chat/messageExtractor
 * @param {Object} messageData - Normalized message data from extractMessageData()
 * @param {string} botUsername - Bot's Telegram username for mention validation
 * @param {Object} webhookData - Raw Telegram webhook payload for additional context
 * @returns {boolean} True if the message should be processed, false if it should be skipped
 *
 * @example
 * // Check if a private chat message should be processed
 * const shouldProcess = shouldProcessMessage(messageData, 'mybot', webhookData);
 * console.log(shouldProcess); // true for valid private chat messages
 *
 * @example
 * // Check if a group chat message requires processing
 * const shouldProcess = shouldProcessMessage(groupMessageData, 'mybot', webhookData);
 * console.log(shouldProcess); // true only if bot is mentioned in group text messages
 */
export function shouldProcessMessage(messageData, botUsername, webhookData) {
	// Extract core message properties for easier access
	const { chatId, text, photo, document, chatType, mediaGroupId, replyToPhoto, replyToDocument } = messageData;

	if (!chatId) {
		console.log('Skipping processing: No valid chat ID.');
		return false;
	}

	// Skip media group messages that lack a caption to process
	if (mediaGroupId && !text) {
		console.log(`Skipping processing for chat ${chatId}: Media group message without caption.`);
		return false;
	}

	// Verify the message contains processable content
	// This includes text, photos, documents, or content in replied-to messages
	const hasContent = photo.length > 0 || document.file_id || text || replyToPhoto.length > 0 || replyToDocument.file_id;
	if (!hasContent) {
		console.log(`Skipping processing for chat ${chatId}: No text, photo, document, or replied-to media content.`);
		return false;
	}

	// Process messages with media (current or replied-to) without requiring a mention
	if (photo.length > 0 || document.file_id || replyToPhoto.length > 0 || replyToDocument.file_id) {
		return true;
	}

	// In private chats, process all messages with content
	if (chatType === 'private') {
		return true;
	}

	// For text-only messages in group chats, require a bot mention
	if (text) {
		const isBotMentioned = checkBotMention(webhookData, botUsername);
		if (!isBotMentioned) {
			console.log(`Skipping processing for chat ${chatId}: Bot not mentioned in group chat.`);
			return false;
		}
		return true;
	}

	console.log(`Skipping processing for chat ${chatId}: Unhandled case.`);
	return false;
}
