{
	"lockfileVersion": 1,
	"workspaces": {
		"": {
			"name": "harmony-agent",
			"dependencies": {
				"@langchain/community": "^0.3.50",
				"@langchain/core": "^0.3.71",
				"@langchain/exa": "^0.1.0",
				"@langchain/google-common": "^0.2.12",
				"@langchain/google-genai": "^0.2.12",
				"@langchain/langgraph": "^0.4.5",
				"@langchain/langgraph-checkpoint-postgres": "^0.1.1",
				"@langchain/openai": "^0.6.7",
				"@upstash/redis": "^1.35.3",
				"@upstash/vector": "^1.2.2",
				"@vercel/functions": "^2.2.12",
				"hono": "^4.9.2",
				"langchain": "^0.3.30",
				"langfuse-langchain": "^3.38.4",
				"nanoid": "^5.1.5",
				"pg": "^8.16.3",
			},
			"devDependencies": {
				"@eslint/js": "^9.29.0",
				"eslint": "^9.33.0",
				"eslint-plugin-simple-import-sort": "^12.1.1",
				"globals": "^16.3.0",
				"prettier": "^3.6.2",
				"prettier-plugin-organize-imports": "^4.2.0",
			},
		},
	},
	"packages": {
		"@anthropic-ai/sdk": [
			"@anthropic-ai/sdk@0.27.3",
			"",
			{
				"dependencies": {
					"@types/node": "^18.11.18",
					"@types/node-fetch": "^2.6.4",
					"abort-controller": "^3.0.0",
					"agentkeepalive": "^4.2.1",
					"form-data-encoder": "1.7.2",
					"formdata-node": "^4.3.2",
					"node-fetch": "^2.6.7",
				},
			},
			"sha512-IjLt0gd3L4jlOfilxVXTifn42FnVffMgDC04RJK1KDZpmkBWLv0XC92MVVmkxrFZNS/7l3xWgP/I3nqtX1sQHw==",
		],

		"@browserbasehq/sdk": [
			"@browserbasehq/sdk@2.6.0",
			"",
			{
				"dependencies": {
					"@types/node": "^18.11.18",
					"@types/node-fetch": "^2.6.4",
					"abort-controller": "^3.0.0",
					"agentkeepalive": "^4.2.1",
					"form-data-encoder": "1.7.2",
					"formdata-node": "^4.3.2",
					"node-fetch": "^2.6.7",
				},
			},
			"sha512-83iXP5D7xMm8Wyn66TUaUrgoByCmAJuoMoZQI3sGg3JAiMlTfnCIMqyVBoNSaItaPIkaCnrsj6LiusmXV2X9YA==",
		],

		"@browserbasehq/stagehand": [
			"@browserbasehq/stagehand@1.14.0",
			"",
			{
				"dependencies": {
					"@anthropic-ai/sdk": "^0.27.3",
					"@browserbasehq/sdk": "^2.0.0",
					"ws": "^8.18.0",
					"zod-to-json-schema": "^3.23.5",
				},
				"peerDependencies": {
					"@playwright/test": "^1.42.1",
					"deepmerge": "^4.3.1",
					"dotenv": "^16.4.5",
					"openai": "^4.62.1",
					"zod": "^3.23.8",
				},
			},
			"sha512-Hi/EzgMFWz+FKyepxHTrqfTPjpsuBS4zRy3e9sbMpBgLPv+9c0R+YZEvS7Bw4mTS66QtvvURRT6zgDGFotthVQ==",
		],

		"@cfworker/json-schema": [
			"@cfworker/json-schema@4.1.1",
			"",
			{},
			"sha512-gAmrUZSGtKc3AiBL71iNWxDsyUC5uMaKKGdvzYsBoTW/xi42JQHl7eKV2OYzCUqvc+D2RCcf7EXY2iCyFIk6og==",
		],

		"@eslint-community/eslint-utils": [
			"@eslint-community/eslint-utils@4.7.0",
			"",
			{ "dependencies": { "eslint-visitor-keys": "^3.4.3" }, "peerDependencies": { "eslint": "^6.0.0 || ^7.0.0 || >=8.0.0" } },
			"sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==",
		],

		"@eslint-community/regexpp": [
			"@eslint-community/regexpp@4.12.1",
			"",
			{},
			"sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==",
		],

		"@eslint/config-array": [
			"@eslint/config-array@0.21.0",
			"",
			{ "dependencies": { "@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2" } },
			"sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==",
		],

		"@eslint/config-helpers": [
			"@eslint/config-helpers@0.3.1",
			"",
			{},
			"sha512-xR93k9WhrDYpXHORXpxVL5oHj3Era7wo6k/Wd8/IsQNnZUTzkGS29lyn3nAT05v6ltUuTFVCCYDEGfy2Or/sPA==",
		],

		"@eslint/core": [
			"@eslint/core@0.15.2",
			"",
			{ "dependencies": { "@types/json-schema": "^7.0.15" } },
			"sha512-78Md3/Rrxh83gCxoUc0EiciuOHsIITzLy53m3d9UyiW8y9Dj2D29FeETqyKA+BRK76tnTp6RXWb3pCay8Oyomg==",
		],

		"@eslint/eslintrc": [
			"@eslint/eslintrc@3.3.1",
			"",
			{
				"dependencies": {
					"ajv": "^6.12.4",
					"debug": "^4.3.2",
					"espree": "^10.0.1",
					"globals": "^14.0.0",
					"ignore": "^5.2.0",
					"import-fresh": "^3.2.1",
					"js-yaml": "^4.1.0",
					"minimatch": "^3.1.2",
					"strip-json-comments": "^3.1.1",
				},
			},
			"sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==",
		],

		"@eslint/js": [
			"@eslint/js@9.33.0",
			"",
			{},
			"sha512-5K1/mKhWaMfreBGJTwval43JJmkip0RmM+3+IuqupeSKNC/Th2Kc7ucaq5ovTSra/OOKB9c58CGSz3QMVbWt0A==",
		],

		"@eslint/object-schema": [
			"@eslint/object-schema@2.1.6",
			"",
			{},
			"sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==",
		],

		"@eslint/plugin-kit": [
			"@eslint/plugin-kit@0.3.5",
			"",
			{ "dependencies": { "@eslint/core": "^0.15.2", "levn": "^0.4.1" } },
			"sha512-Z5kJ+wU3oA7MMIqVR9tyZRtjYPr4OC004Q4Rw7pgOKUOKkJfZ3O24nz3WYfGRpMDNmcOi3TwQOmgm7B7Tpii0w==",
		],

		"@google/generative-ai": [
			"@google/generative-ai@0.24.1",
			"",
			{},
			"sha512-MqO+MLfM6kjxcKoy0p1wRzG3b4ZZXtPI+z2IE26UogS2Cm/XHO+7gGRBh6gcJsOiIVoH93UwKvW4HdgiOZCy9Q==",
		],

		"@graphql-typed-document-node/core": [
			"@graphql-typed-document-node/core@3.2.0",
			"",
			{
				"peerDependencies": {
					"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0",
				},
			},
			"sha512-mB9oAsNCm9aM3/SOv4YtBMqZbYj10R7dkq8byBqxGY/ncFwhf2oQzMV+LCRlWoDSEBJ3COiR1yeDvMtsoOsuFQ==",
		],

		"@grpc/grpc-js": [
			"@grpc/grpc-js@1.13.4",
			"",
			{ "dependencies": { "@grpc/proto-loader": "^0.7.13", "@js-sdsl/ordered-map": "^4.4.2" } },
			"sha512-GsFaMXCkMqkKIvwCQjCrwH+GHbPKBjhwo/8ZuUkWHqbI73Kky9I+pQltrlT0+MWpedCoosda53lgjYfyEPgxBg==",
		],

		"@grpc/proto-loader": [
			"@grpc/proto-loader@0.7.15",
			"",
			{
				"dependencies": { "lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2" },
				"bin": { "proto-loader-gen-types": "build/bin/proto-loader-gen-types.js" },
			},
			"sha512-tMXdRCfYVixjuFK+Hk0Q1s38gV9zDiDJfWL3h1rv4Qc39oILCu1TRTDt7+fGUI8K4G1Fj125Hx/ru3azECWTyQ==",
		],

		"@humanfs/core": [
			"@humanfs/core@0.19.1",
			"",
			{},
			"sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==",
		],

		"@humanfs/node": [
			"@humanfs/node@0.16.6",
			"",
			{ "dependencies": { "@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0" } },
			"sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==",
		],

		"@humanwhocodes/module-importer": [
			"@humanwhocodes/module-importer@1.0.1",
			"",
			{},
			"sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==",
		],

		"@humanwhocodes/retry": [
			"@humanwhocodes/retry@0.4.3",
			"",
			{},
			"sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==",
		],

		"@ibm-cloud/watsonx-ai": [
			"@ibm-cloud/watsonx-ai@1.6.10",
			"",
			{ "dependencies": { "@types/node": "^18.0.0", "extend": "3.0.2", "ibm-cloud-sdk-core": "^5.3.2" } },
			"sha512-aZV50/s8VZc7w0t/qcaBw3RLT3WDsAeZUJlP8EbG/csZJF3a8F7alihbGOM4lJFM7R4Z81Lucz3nfHi2KR7J4Q==",
		],

		"@js-sdsl/ordered-map": [
			"@js-sdsl/ordered-map@4.4.2",
			"",
			{},
			"sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==",
		],

		"@langchain/community": [
			"@langchain/community@0.3.50",
			"",
			{
				"dependencies": {
					"@langchain/openai": ">=0.2.0 <0.7.0",
					"@langchain/weaviate": "^0.2.0",
					"binary-extensions": "^2.2.0",
					"expr-eval": "^2.0.2",
					"flat": "^5.0.2",
					"js-yaml": "^4.1.0",
					"langchain": ">=0.2.3 <0.3.0 || >=0.3.4 <0.4.0",
					"langsmith": "^0.3.46",
					"uuid": "^10.0.0",
					"zod": "^3.25.32",
				},
				"peerDependencies": {
					"@arcjet/redact": "^v1.0.0-alpha.23",
					"@aws-crypto/sha256-js": "^5.0.0",
					"@aws-sdk/client-bedrock-agent-runtime": "^3.749.0",
					"@aws-sdk/client-bedrock-runtime": "^3.749.0",
					"@aws-sdk/client-dynamodb": "^3.749.0",
					"@aws-sdk/client-kendra": "^3.749.0",
					"@aws-sdk/client-lambda": "^3.749.0",
					"@aws-sdk/client-s3": "^3.749.0",
					"@aws-sdk/client-sagemaker-runtime": "^3.749.0",
					"@aws-sdk/client-sfn": "^3.749.0",
					"@aws-sdk/credential-provider-node": "^3.388.0",
					"@azure/search-documents": "^12.0.0",
					"@azure/storage-blob": "^12.15.0",
					"@browserbasehq/sdk": "*",
					"@browserbasehq/stagehand": "^1.0.0",
					"@clickhouse/client": "^0.2.5",
					"@cloudflare/ai": "*",
					"@datastax/astra-db-ts": "^1.0.0",
					"@elastic/elasticsearch": "^8.4.0",
					"@getmetal/metal-sdk": "*",
					"@getzep/zep-cloud": "^1.0.6",
					"@getzep/zep-js": "^0.9.0",
					"@gomomento/sdk": "^1.51.1",
					"@gomomento/sdk-core": "^1.51.1",
					"@google-ai/generativelanguage": "*",
					"@google-cloud/storage": "^6.10.1 || ^7.7.0",
					"@gradientai/nodejs-sdk": "^1.2.0",
					"@huggingface/inference": "^4.0.5",
					"@huggingface/transformers": "^3.5.2",
					"@ibm-cloud/watsonx-ai": "*",
					"@lancedb/lancedb": "^0.12.0",
					"@langchain/core": ">=0.3.58 <0.4.0",
					"@layerup/layerup-security": "^1.5.12",
					"@libsql/client": "^0.14.0",
					"@mendable/firecrawl-js": "^1.4.3",
					"@mlc-ai/web-llm": "*",
					"@mozilla/readability": "*",
					"@neondatabase/serverless": "*",
					"@notionhq/client": "^2.2.10",
					"@opensearch-project/opensearch": "*",
					"@pinecone-database/pinecone": "*",
					"@planetscale/database": "^1.8.0",
					"@premai/prem-sdk": "^0.3.25",
					"@qdrant/js-client-rest": "^1.15.0",
					"@raycast/api": "^1.55.2",
					"@rockset/client": "^0.9.1",
					"@smithy/eventstream-codec": "^2.0.5",
					"@smithy/protocol-http": "^3.0.6",
					"@smithy/signature-v4": "^2.0.10",
					"@smithy/util-utf8": "^2.0.0",
					"@spider-cloud/spider-client": "^0.0.21",
					"@supabase/supabase-js": "^2.45.0",
					"@tensorflow-models/universal-sentence-encoder": "*",
					"@tensorflow/tfjs-converter": "*",
					"@tensorflow/tfjs-core": "*",
					"@upstash/ratelimit": "^1.1.3 || ^2.0.3",
					"@upstash/redis": "^1.20.6",
					"@upstash/vector": "^1.1.1",
					"@vercel/kv": "*",
					"@vercel/postgres": "*",
					"@writerai/writer-sdk": "^0.40.2",
					"@xata.io/client": "^0.28.0",
					"@zilliz/milvus2-sdk-node": ">=2.3.5",
					"apify-client": "^2.7.1",
					"assemblyai": "^4.6.0",
					"azion": "^1.11.1",
					"better-sqlite3": ">=9.4.0 <12.0.0",
					"cassandra-driver": "^4.7.2",
					"cborg": "^4.1.1",
					"cheerio": "^1.0.0-rc.12",
					"chromadb": "*",
					"closevector-common": "0.1.3",
					"closevector-node": "0.1.6",
					"closevector-web": "0.1.6",
					"cohere-ai": "*",
					"convex": "^1.3.1",
					"crypto-js": "^4.2.0",
					"d3-dsv": "^2.0.0",
					"discord.js": "^14.14.1",
					"dria": "^0.0.3",
					"duck-duck-scrape": "^2.2.5",
					"epub2": "^3.0.1",
					"fast-xml-parser": "*",
					"firebase-admin": "^11.9.0 || ^12.0.0",
					"google-auth-library": "*",
					"googleapis": "*",
					"hnswlib-node": "^3.0.0",
					"html-to-text": "^9.0.5",
					"ibm-cloud-sdk-core": "*",
					"ignore": "^5.2.0",
					"interface-datastore": "^8.2.11",
					"ioredis": "^5.3.2",
					"it-all": "^3.0.4",
					"jsdom": "*",
					"jsonwebtoken": "^9.0.2",
					"llmonitor": "^0.5.9",
					"lodash": "^4.17.21",
					"lunary": "^0.7.10",
					"mammoth": "^1.6.0",
					"mariadb": "^3.4.0",
					"mem0ai": "^2.1.8",
					"mongodb": "^6.17.0",
					"mysql2": "^3.9.8",
					"neo4j-driver": "*",
					"notion-to-md": "^3.1.0",
					"officeparser": "^4.0.4",
					"openai": "*",
					"pdf-parse": "1.1.1",
					"pg": "^8.11.0",
					"pg-copy-streams": "^6.0.5",
					"pickleparser": "^0.2.1",
					"playwright": "^1.32.1",
					"portkey-ai": "^0.1.11",
					"puppeteer": "*",
					"pyodide": ">=0.24.1 <0.27.0",
					"redis": "*",
					"replicate": "*",
					"sonix-speech-recognition": "^2.1.1",
					"srt-parser-2": "^1.2.3",
					"typeorm": "^0.3.20",
					"typesense": "^1.5.3",
					"usearch": "^1.1.1",
					"voy-search": "0.6.2",
					"weaviate-client": "^3.5.2",
					"web-auth-library": "^1.0.3",
					"word-extractor": "*",
					"ws": "^8.14.2",
					"youtubei.js": "*",
				},
				"optionalPeers": [
					"@arcjet/redact",
					"@aws-crypto/sha256-js",
					"@aws-sdk/client-bedrock-agent-runtime",
					"@aws-sdk/client-bedrock-runtime",
					"@aws-sdk/client-dynamodb",
					"@aws-sdk/client-kendra",
					"@aws-sdk/client-lambda",
					"@aws-sdk/client-s3",
					"@aws-sdk/client-sagemaker-runtime",
					"@aws-sdk/client-sfn",
					"@aws-sdk/credential-provider-node",
					"@azure/search-documents",
					"@azure/storage-blob",
					"@clickhouse/client",
					"@cloudflare/ai",
					"@datastax/astra-db-ts",
					"@elastic/elasticsearch",
					"@getmetal/metal-sdk",
					"@getzep/zep-cloud",
					"@getzep/zep-js",
					"@gomomento/sdk",
					"@gomomento/sdk-core",
					"@google-ai/generativelanguage",
					"@google-cloud/storage",
					"@gradientai/nodejs-sdk",
					"@huggingface/inference",
					"@huggingface/transformers",
					"@lancedb/lancedb",
					"@layerup/layerup-security",
					"@libsql/client",
					"@mendable/firecrawl-js",
					"@mlc-ai/web-llm",
					"@mozilla/readability",
					"@neondatabase/serverless",
					"@notionhq/client",
					"@opensearch-project/opensearch",
					"@pinecone-database/pinecone",
					"@planetscale/database",
					"@premai/prem-sdk",
					"@qdrant/js-client-rest",
					"@raycast/api",
					"@rockset/client",
					"@smithy/eventstream-codec",
					"@smithy/protocol-http",
					"@smithy/signature-v4",
					"@smithy/util-utf8",
					"@spider-cloud/spider-client",
					"@supabase/supabase-js",
					"@tensorflow-models/universal-sentence-encoder",
					"@tensorflow/tfjs-converter",
					"@tensorflow/tfjs-core",
					"@upstash/ratelimit",
					"@vercel/kv",
					"@vercel/postgres",
					"@writerai/writer-sdk",
					"@xata.io/client",
					"@zilliz/milvus2-sdk-node",
					"apify-client",
					"assemblyai",
					"azion",
					"better-sqlite3",
					"cassandra-driver",
					"cborg",
					"cheerio",
					"chromadb",
					"closevector-common",
					"closevector-node",
					"closevector-web",
					"cohere-ai",
					"convex",
					"crypto-js",
					"d3-dsv",
					"discord.js",
					"dria",
					"duck-duck-scrape",
					"epub2",
					"fast-xml-parser",
					"firebase-admin",
					"google-auth-library",
					"googleapis",
					"hnswlib-node",
					"html-to-text",
					"interface-datastore",
					"ioredis",
					"it-all",
					"jsdom",
					"llmonitor",
					"lodash",
					"lunary",
					"mammoth",
					"mariadb",
					"mem0ai",
					"mongodb",
					"mysql2",
					"neo4j-driver",
					"notion-to-md",
					"officeparser",
					"pdf-parse",
					"pg-copy-streams",
					"pickleparser",
					"portkey-ai",
					"puppeteer",
					"pyodide",
					"redis",
					"replicate",
					"sonix-speech-recognition",
					"srt-parser-2",
					"typeorm",
					"typesense",
					"usearch",
					"voy-search",
					"web-auth-library",
					"word-extractor",
					"youtubei.js",
				],
			},
			"sha512-3tni++DmYV1Xb4AYZmky4he8lMxrTrkOT+/RSVin5gAwEN5e0QEeNmipWpcKRrmDNUsZZxGdYRPN5Wo23hDqBA==",
		],

		"@langchain/core": [
			"@langchain/core@0.3.71",
			"",
			{
				"dependencies": {
					"@cfworker/json-schema": "^4.0.2",
					"ansi-styles": "^5.0.0",
					"camelcase": "6",
					"decamelize": "1.2.0",
					"js-tiktoken": "^1.0.12",
					"langsmith": "^0.3.46",
					"mustache": "^4.2.0",
					"p-queue": "^6.6.2",
					"p-retry": "4",
					"uuid": "^10.0.0",
					"zod": "^3.25.32",
					"zod-to-json-schema": "^3.22.3",
				},
			},
			"sha512-ejArFmm/lZ9je2rbMLpkFZnva8jkvDhFCn370WAAVP8akbPOLgDA+S2e5jvj+G/oHraOwmviY8EnxbPXXcDvfw==",
		],

		"@langchain/exa": [
			"@langchain/exa@0.1.0",
			"",
			{ "dependencies": { "exa-js": "^1.0.12" }, "peerDependencies": { "@langchain/core": ">=0.2.21 <0.4.0" } },
			"sha512-9HN4vuqiKxo4SYu7SbxdrKAu2ISiDUy0PRexKX/pNbxwAzKhBhCDkALjIceSAIJ9F6rgJxkykPYJd1AzHPvXFA==",
		],

		"@langchain/google-common": [
			"@langchain/google-common@0.2.12",
			"",
			{ "dependencies": { "uuid": "^10.0.0" }, "peerDependencies": { "@langchain/core": ">=0.3.58 <0.4.0" } },
			"sha512-I1nHn19wSFfnjBCFFvrYmpzq7bPcKHeR2MD84YonyL8uFdIFY9KP+QDZBOCLm1Gi1C+SDG20135mGevi0BsNVA==",
		],

		"@langchain/google-genai": [
			"@langchain/google-genai@0.2.12",
			"",
			{
				"dependencies": { "@google/generative-ai": "^0.24.0", "uuid": "^11.1.0" },
				"peerDependencies": { "@langchain/core": ">=0.3.58 <0.4.0" },
			},
			"sha512-dVfkNW3uJ2Ing4KAYPTfkdTDIA4FDrik/YK5A5bE8w7drP0J+7g0h2i8D5Mn7dDcyPuX74qd2VG9hWCyGLtZiw==",
		],

		"@langchain/langgraph": [
			"@langchain/langgraph@0.4.5",
			"",
			{
				"dependencies": {
					"@langchain/langgraph-checkpoint": "^0.1.0",
					"@langchain/langgraph-sdk": "~0.0.107",
					"uuid": "^10.0.0",
					"zod": "^3.25.32",
				},
				"peerDependencies": { "@langchain/core": ">=0.3.58 < 0.4.0", "zod-to-json-schema": "^3.x" },
			},
			"sha512-LpKDe/SROC9/qIPkuGiyBkIeuPJ4FxWljaJPd8P2cp6IjYI8UqWk7w6IHiq+JIB1oPPcKGprc3D4OZjq59WHzQ==",
		],

		"@langchain/langgraph-checkpoint": [
			"@langchain/langgraph-checkpoint@0.1.0",
			"",
			{ "dependencies": { "uuid": "^10.0.0" }, "peerDependencies": { "@langchain/core": ">=0.2.31 <0.4.0" } },
			"sha512-7oY5b0VQSxcV3DgoHdXiCgBhEzml/ZjZfKNeuq6oZ3ggcdUICa/fzrIBbFju6gxPU8ly93s0OsjF4yURnHw70Q==",
		],

		"@langchain/langgraph-checkpoint-postgres": [
			"@langchain/langgraph-checkpoint-postgres@0.1.1",
			"",
			{
				"dependencies": { "pg": "^8.12.0" },
				"peerDependencies": { "@langchain/core": ">=0.2.31 <0.4.0", "@langchain/langgraph-checkpoint": "^0.1.0" },
			},
			"sha512-+Woc9yjSwG0ejG/GWcA75j1Fe4eNVCI0wN1psuZ3YarwhjSyEXtAbVPV92soAmKo10AdUpMyANYgutFfYxryzw==",
		],

		"@langchain/langgraph-sdk": [
			"@langchain/langgraph-sdk@0.0.107",
			"",
			{
				"dependencies": { "@types/json-schema": "^7.0.15", "p-queue": "^6.6.2", "p-retry": "4", "uuid": "^9.0.0" },
				"peerDependencies": { "@langchain/core": ">=0.2.31 <0.4.0", "react": "^18 || ^19", "react-dom": "^18 || ^19" },
				"optionalPeers": ["react", "react-dom"],
			},
			"sha512-2qzboDgYH8KJNz7q2Yzvj6H9i4iZUYfZnB7xY+Dkye6yvI+2m1fFIdpP/Ppu+eFvoIUAsbDHDF+wvR4F11kS3Q==",
		],

		"@langchain/openai": [
			"@langchain/openai@0.6.7",
			"",
			{
				"dependencies": { "js-tiktoken": "^1.0.12", "openai": "^5.12.1", "zod": "^3.25.32" },
				"peerDependencies": { "@langchain/core": ">=0.3.68 <0.4.0" },
			},
			"sha512-mNT9AdfEvDjlWU76hEl1HgTFkgk7yFKdIRgQz3KXKZhEERXhAwYJNgPFq8+HIpgxYSnc12akZ1uo8WPS98ErPQ==",
		],

		"@langchain/textsplitters": [
			"@langchain/textsplitters@0.1.0",
			"",
			{ "dependencies": { "js-tiktoken": "^1.0.12" }, "peerDependencies": { "@langchain/core": ">=0.2.21 <0.4.0" } },
			"sha512-djI4uw9rlkAb5iMhtLED+xJebDdAG935AdP4eRTB02R7OB/act55Bj9wsskhZsvuyQRpO4O1wQOp85s6T6GWmw==",
		],

		"@langchain/weaviate": [
			"@langchain/weaviate@0.2.1",
			"",
			{
				"dependencies": { "uuid": "^10.0.0", "weaviate-client": "^3.5.2" },
				"peerDependencies": { "@langchain/core": ">=0.2.21 <0.4.0" },
			},
			"sha512-rlfAKF+GB0A5MUrol34oDrBkl4q6AefARk9KDW+LfzhV/74pZZLZyIPYPxvE4XwI3gvpwp024DNsDxK/4UW0/g==",
		],

		"@playwright/test": [
			"@playwright/test@1.54.2",
			"",
			{ "dependencies": { "playwright": "1.54.2" }, "bin": { "playwright": "cli.js" } },
			"sha512-A+znathYxPf+72riFd1r1ovOLqsIIB0jKIoPjyK2kqEIe30/6jF6BC7QNluHuwUmsD2tv1XZVugN8GqfTMOxsA==",
		],

		"@protobufjs/aspromise": [
			"@protobufjs/aspromise@1.1.2",
			"",
			{},
			"sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==",
		],

		"@protobufjs/base64": [
			"@protobufjs/base64@1.1.2",
			"",
			{},
			"sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==",
		],

		"@protobufjs/codegen": [
			"@protobufjs/codegen@2.0.4",
			"",
			{},
			"sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==",
		],

		"@protobufjs/eventemitter": [
			"@protobufjs/eventemitter@1.1.0",
			"",
			{},
			"sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==",
		],

		"@protobufjs/fetch": [
			"@protobufjs/fetch@1.1.0",
			"",
			{ "dependencies": { "@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0" } },
			"sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==",
		],

		"@protobufjs/float": [
			"@protobufjs/float@1.0.2",
			"",
			{},
			"sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==",
		],

		"@protobufjs/inquire": [
			"@protobufjs/inquire@1.1.0",
			"",
			{},
			"sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==",
		],

		"@protobufjs/path": [
			"@protobufjs/path@1.1.2",
			"",
			{},
			"sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==",
		],

		"@protobufjs/pool": [
			"@protobufjs/pool@1.1.0",
			"",
			{},
			"sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==",
		],

		"@protobufjs/utf8": [
			"@protobufjs/utf8@1.1.0",
			"",
			{},
			"sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==",
		],

		"@tokenizer/token": [
			"@tokenizer/token@0.3.0",
			"",
			{},
			"sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==",
		],

		"@types/debug": [
			"@types/debug@4.1.12",
			"",
			{ "dependencies": { "@types/ms": "*" } },
			"sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==",
		],

		"@types/estree": [
			"@types/estree@1.0.8",
			"",
			{},
			"sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==",
		],

		"@types/json-schema": [
			"@types/json-schema@7.0.15",
			"",
			{},
			"sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==",
		],

		"@types/ms": [
			"@types/ms@2.1.0",
			"",
			{},
			"sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==",
		],

		"@types/node": [
			"@types/node@18.19.122",
			"",
			{ "dependencies": { "undici-types": "~5.26.4" } },
			"sha512-yzegtT82dwTNEe/9y+CM8cgb42WrUfMMCg2QqSddzO1J6uPmBD7qKCZ7dOHZP2Yrpm/kb0eqdNMn2MUyEiqBmA==",
		],

		"@types/node-fetch": [
			"@types/node-fetch@2.6.12",
			"",
			{ "dependencies": { "@types/node": "*", "form-data": "^4.0.0" } },
			"sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==",
		],

		"@types/retry": [
			"@types/retry@0.12.0",
			"",
			{},
			"sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==",
		],

		"@types/tough-cookie": [
			"@types/tough-cookie@4.0.5",
			"",
			{},
			"sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==",
		],

		"@types/uuid": [
			"@types/uuid@10.0.0",
			"",
			{},
			"sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==",
		],

		"@upstash/redis": [
			"@upstash/redis@1.35.3",
			"",
			{ "dependencies": { "uncrypto": "^0.1.3" } },
			"sha512-hSjv66NOuahW3MisRGlSgoszU2uONAY2l5Qo3Sae8OT3/Tng9K+2/cBRuyPBX8egwEGcNNCF9+r0V6grNnhL+w==",
		],

		"@upstash/vector": [
			"@upstash/vector@1.2.2",
			"",
			{},
			"sha512-ptQ9xnxtKqmpNK52PCcHCszlPOLxIBfjsv7ty8RoF95pkjctS9rSjTQ3Pl9bx5VFbpDj+0dMXw88WLt6swDkgQ==",
		],

		"@vercel/functions": [
			"@vercel/functions@2.2.12",
			"",
			{
				"dependencies": { "@vercel/oidc": "2.0.1" },
				"peerDependencies": { "@aws-sdk/credential-provider-web-identity": "*" },
				"optionalPeers": ["@aws-sdk/credential-provider-web-identity"],
			},
			"sha512-WGGqro/Rg00Epj+t2l6lr68q6ZkFt5+Q4F4Ok8sJbYrpu5pniDay09ihJqUoz81NI9PIfIahGEjaKpucUhEIrg==",
		],

		"@vercel/oidc": [
			"@vercel/oidc@2.0.1",
			"",
			{ "dependencies": { "@types/ms": "2.1.0", "ms": "2.1.3" } },
			"sha512-p/rFk8vz+AggU0bHXjwtRUyXNxboLvfCjwN0KH5xhBJ5wGS+n/psLJP1c69QPdWIZM4aVVIrTqdjUuDwuJGYzQ==",
		],

		"abort-controller": [
			"abort-controller@3.0.0",
			"",
			{ "dependencies": { "event-target-shim": "^5.0.0" } },
			"sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==",
		],

		"abort-controller-x": [
			"abort-controller-x@0.4.3",
			"",
			{},
			"sha512-VtUwTNU8fpMwvWGn4xE93ywbogTYsuT+AUxAXOeelbXuQVIwNmC5YLeho9sH4vZ4ITW8414TTAOG1nW6uIVHCA==",
		],

		"acorn": [
			"acorn@8.15.0",
			"",
			{ "bin": "bin/acorn" },
			"sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==",
		],

		"acorn-jsx": [
			"acorn-jsx@5.3.2",
			"",
			{ "peerDependencies": { "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0" } },
			"sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",
		],

		"agentkeepalive": [
			"agentkeepalive@4.6.0",
			"",
			{ "dependencies": { "humanize-ms": "^1.2.1" } },
			"sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==",
		],

		"ajv": [
			"ajv@6.12.6",
			"",
			{
				"dependencies": {
					"fast-deep-equal": "^3.1.1",
					"fast-json-stable-stringify": "^2.0.0",
					"json-schema-traverse": "^0.4.1",
					"uri-js": "^4.2.2",
				},
			},
			"sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==",
		],

		"ansi-regex": [
			"ansi-regex@5.0.1",
			"",
			{},
			"sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
		],

		"ansi-styles": [
			"ansi-styles@5.2.0",
			"",
			{},
			"sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==",
		],

		"argparse": [
			"argparse@2.0.1",
			"",
			{},
			"sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",
		],

		"asynckit": [
			"asynckit@0.4.0",
			"",
			{},
			"sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==",
		],

		"axios": [
			"axios@1.11.0",
			"",
			{ "dependencies": { "follow-redirects": "^1.15.6", "form-data": "^4.0.4", "proxy-from-env": "^1.1.0" } },
			"sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==",
		],

		"balanced-match": [
			"balanced-match@1.0.2",
			"",
			{},
			"sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
		],

		"base64-js": [
			"base64-js@1.5.1",
			"",
			{},
			"sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==",
		],

		"binary-extensions": [
			"binary-extensions@2.3.0",
			"",
			{},
			"sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",
		],

		"brace-expansion": [
			"brace-expansion@1.1.12",
			"",
			{ "dependencies": { "balanced-match": "^1.0.0", "concat-map": "0.0.1" } },
			"sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",
		],

		"buffer": [
			"buffer@6.0.3",
			"",
			{ "dependencies": { "base64-js": "^1.3.1", "ieee754": "^1.2.1" } },
			"sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==",
		],

		"buffer-equal-constant-time": [
			"buffer-equal-constant-time@1.0.1",
			"",
			{},
			"sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==",
		],

		"call-bind-apply-helpers": [
			"call-bind-apply-helpers@1.0.2",
			"",
			{ "dependencies": { "es-errors": "^1.3.0", "function-bind": "^1.1.2" } },
			"sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==",
		],

		"callsites": [
			"callsites@3.1.0",
			"",
			{},
			"sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==",
		],

		"camelcase": [
			"camelcase@6.3.0",
			"",
			{},
			"sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==",
		],

		"chalk": [
			"chalk@4.1.2",
			"",
			{ "dependencies": { "ansi-styles": "^4.1.0", "supports-color": "^7.1.0" } },
			"sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
		],

		"cliui": [
			"cliui@8.0.1",
			"",
			{ "dependencies": { "string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0" } },
			"sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==",
		],

		"color-convert": [
			"color-convert@2.0.1",
			"",
			{ "dependencies": { "color-name": "~1.1.4" } },
			"sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
		],

		"color-name": [
			"color-name@1.1.4",
			"",
			{},
			"sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
		],

		"combined-stream": [
			"combined-stream@1.0.8",
			"",
			{ "dependencies": { "delayed-stream": "~1.0.0" } },
			"sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==",
		],

		"concat-map": [
			"concat-map@0.0.1",
			"",
			{},
			"sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",
		],

		"console-table-printer": [
			"console-table-printer@2.14.3",
			"",
			{ "dependencies": { "simple-wcswidth": "^1.0.1" } },
			"sha512-X5OCFnjYlXzRuC8ac5hPA2QflRjJvNKJocMhlnqK/Ap7q3DHXr0NJ0TGzwmEKOiOdJrjsSwEd0m+a32JAYPrKQ==",
		],

		"cross-fetch": [
			"cross-fetch@4.1.0",
			"",
			{ "dependencies": { "node-fetch": "^2.7.0" } },
			"sha512-uKm5PU+MHTootlWEY+mZ4vvXoCn4fLQxT9dSc1sXVMSFkINTJVN8cAQROpwcKm8bJ/c7rgZVIBWzH5T78sNZZw==",
		],

		"cross-spawn": [
			"cross-spawn@7.0.6",
			"",
			{ "dependencies": { "path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1" } },
			"sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",
		],

		"debug": [
			"debug@4.4.1",
			"",
			{ "dependencies": { "ms": "^2.1.3" } },
			"sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==",
		],

		"decamelize": [
			"decamelize@1.2.0",
			"",
			{},
			"sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==",
		],

		"deep-is": [
			"deep-is@0.1.4",
			"",
			{},
			"sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==",
		],

		"deepmerge": [
			"deepmerge@4.3.1",
			"",
			{},
			"sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==",
		],

		"delayed-stream": [
			"delayed-stream@1.0.0",
			"",
			{},
			"sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==",
		],

		"dotenv": [
			"dotenv@16.6.1",
			"",
			{},
			"sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==",
		],

		"dunder-proto": [
			"dunder-proto@1.0.1",
			"",
			{ "dependencies": { "call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0" } },
			"sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==",
		],

		"ecdsa-sig-formatter": [
			"ecdsa-sig-formatter@1.0.11",
			"",
			{ "dependencies": { "safe-buffer": "^5.0.1" } },
			"sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==",
		],

		"emoji-regex": [
			"emoji-regex@8.0.0",
			"",
			{},
			"sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
		],

		"es-define-property": [
			"es-define-property@1.0.1",
			"",
			{},
			"sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==",
		],

		"es-errors": [
			"es-errors@1.3.0",
			"",
			{},
			"sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==",
		],

		"es-object-atoms": [
			"es-object-atoms@1.1.1",
			"",
			{ "dependencies": { "es-errors": "^1.3.0" } },
			"sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==",
		],

		"es-set-tostringtag": [
			"es-set-tostringtag@2.1.0",
			"",
			{ "dependencies": { "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2" } },
			"sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==",
		],

		"escalade": [
			"escalade@3.2.0",
			"",
			{},
			"sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",
		],

		"escape-string-regexp": [
			"escape-string-regexp@4.0.0",
			"",
			{},
			"sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==",
		],

		"eslint": [
			"eslint@9.33.0",
			"",
			{
				"dependencies": {
					"@eslint-community/eslint-utils": "^4.2.0",
					"@eslint-community/regexpp": "^4.12.1",
					"@eslint/config-array": "^0.21.0",
					"@eslint/config-helpers": "^0.3.1",
					"@eslint/core": "^0.15.2",
					"@eslint/eslintrc": "^3.3.1",
					"@eslint/js": "9.33.0",
					"@eslint/plugin-kit": "^0.3.5",
					"@humanfs/node": "^0.16.6",
					"@humanwhocodes/module-importer": "^1.0.1",
					"@humanwhocodes/retry": "^0.4.2",
					"@types/estree": "^1.0.6",
					"@types/json-schema": "^7.0.15",
					"ajv": "^6.12.4",
					"chalk": "^4.0.0",
					"cross-spawn": "^7.0.6",
					"debug": "^4.3.2",
					"escape-string-regexp": "^4.0.0",
					"eslint-scope": "^8.4.0",
					"eslint-visitor-keys": "^4.2.1",
					"espree": "^10.4.0",
					"esquery": "^1.5.0",
					"esutils": "^2.0.2",
					"fast-deep-equal": "^3.1.3",
					"file-entry-cache": "^8.0.0",
					"find-up": "^5.0.0",
					"glob-parent": "^6.0.2",
					"ignore": "^5.2.0",
					"imurmurhash": "^0.1.4",
					"is-glob": "^4.0.0",
					"json-stable-stringify-without-jsonify": "^1.0.1",
					"lodash.merge": "^4.6.2",
					"minimatch": "^3.1.2",
					"natural-compare": "^1.4.0",
					"optionator": "^0.9.3",
				},
				"peerDependencies": { "jiti": "*" },
				"optionalPeers": ["jiti"],
				"bin": "bin/eslint.js",
			},
			"sha512-TS9bTNIryDzStCpJN93aC5VRSW3uTx9sClUn4B87pwiCaJh220otoI0X8mJKr+VcPtniMdN8GKjlwgWGUv5ZKA==",
		],

		"eslint-plugin-simple-import-sort": [
			"eslint-plugin-simple-import-sort@12.1.1",
			"",
			{ "peerDependencies": { "eslint": ">=5.0.0" } },
			"sha512-6nuzu4xwQtE3332Uz0to+TxDQYRLTKRESSc2hefVT48Zc8JthmN23Gx9lnYhu0FtkRSL1oxny3kJ2aveVhmOVA==",
		],

		"eslint-scope": [
			"eslint-scope@8.4.0",
			"",
			{ "dependencies": { "esrecurse": "^4.3.0", "estraverse": "^5.2.0" } },
			"sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==",
		],

		"eslint-visitor-keys": [
			"eslint-visitor-keys@4.2.1",
			"",
			{},
			"sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==",
		],

		"espree": [
			"espree@10.4.0",
			"",
			{ "dependencies": { "acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1" } },
			"sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==",
		],

		"esquery": [
			"esquery@1.6.0",
			"",
			{ "dependencies": { "estraverse": "^5.1.0" } },
			"sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==",
		],

		"esrecurse": [
			"esrecurse@4.3.0",
			"",
			{ "dependencies": { "estraverse": "^5.2.0" } },
			"sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==",
		],

		"estraverse": [
			"estraverse@5.3.0",
			"",
			{},
			"sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==",
		],

		"esutils": [
			"esutils@2.0.3",
			"",
			{},
			"sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==",
		],

		"event-target-shim": [
			"event-target-shim@5.0.1",
			"",
			{},
			"sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==",
		],

		"eventemitter3": [
			"eventemitter3@4.0.7",
			"",
			{},
			"sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==",
		],

		"events": [
			"events@3.3.0",
			"",
			{},
			"sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==",
		],

		"exa-js": [
			"exa-js@1.8.27",
			"",
			{
				"dependencies": {
					"cross-fetch": "~4.1.0",
					"dotenv": "~16.4.7",
					"openai": "^5.0.1",
					"zod": "^3.22.0",
					"zod-to-json-schema": "^3.20.0",
				},
			},
			"sha512-C0vsC3r5B1vhibXAxtJ9OlCwXTp+AaWFPk3ozLoUBV2VH+Xd+WhkKp8/30i9mkBVJopK/8+lDMv8MyRjkOWm5g==",
		],

		"expr-eval": [
			"expr-eval@2.0.2",
			"",
			{},
			"sha512-4EMSHGOPSwAfBiibw3ndnP0AvjDWLsMvGOvWEZ2F96IGk0bIVdjQisOHxReSkE13mHcfbuCiXw+G4y0zv6N8Eg==",
		],

		"extend": [
			"extend@3.0.2",
			"",
			{},
			"sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==",
		],

		"fast-deep-equal": [
			"fast-deep-equal@3.1.3",
			"",
			{},
			"sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",
		],

		"fast-json-stable-stringify": [
			"fast-json-stable-stringify@2.1.0",
			"",
			{},
			"sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==",
		],

		"fast-levenshtein": [
			"fast-levenshtein@2.0.6",
			"",
			{},
			"sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==",
		],

		"file-entry-cache": [
			"file-entry-cache@8.0.0",
			"",
			{ "dependencies": { "flat-cache": "^4.0.0" } },
			"sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==",
		],

		"file-type": [
			"file-type@16.5.4",
			"",
			{ "dependencies": { "readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1" } },
			"sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==",
		],

		"find-up": [
			"find-up@5.0.0",
			"",
			{ "dependencies": { "locate-path": "^6.0.0", "path-exists": "^4.0.0" } },
			"sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==",
		],

		"flat": [
			"flat@5.0.2",
			"",
			{ "bin": "cli.js" },
			"sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==",
		],

		"flat-cache": [
			"flat-cache@4.0.1",
			"",
			{ "dependencies": { "flatted": "^3.2.9", "keyv": "^4.5.4" } },
			"sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==",
		],

		"flatted": [
			"flatted@3.3.3",
			"",
			{},
			"sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==",
		],

		"follow-redirects": [
			"follow-redirects@1.15.11",
			"",
			{},
			"sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==",
		],

		"form-data": [
			"form-data@4.0.4",
			"",
			{
				"dependencies": {
					"asynckit": "^0.4.0",
					"combined-stream": "^1.0.8",
					"es-set-tostringtag": "^2.1.0",
					"hasown": "^2.0.2",
					"mime-types": "^2.1.12",
				},
			},
			"sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==",
		],

		"form-data-encoder": [
			"form-data-encoder@1.7.2",
			"",
			{},
			"sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==",
		],

		"formdata-node": [
			"formdata-node@4.4.1",
			"",
			{ "dependencies": { "node-domexception": "1.0.0", "web-streams-polyfill": "4.0.0-beta.3" } },
			"sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==",
		],

		"fsevents": [
			"fsevents@2.3.2",
			"",
			{ "os": "darwin" },
			"sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==",
		],

		"function-bind": [
			"function-bind@1.1.2",
			"",
			{},
			"sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
		],

		"get-caller-file": [
			"get-caller-file@2.0.5",
			"",
			{},
			"sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==",
		],

		"get-intrinsic": [
			"get-intrinsic@1.3.0",
			"",
			{
				"dependencies": {
					"call-bind-apply-helpers": "^1.0.2",
					"es-define-property": "^1.0.1",
					"es-errors": "^1.3.0",
					"es-object-atoms": "^1.1.1",
					"function-bind": "^1.1.2",
					"get-proto": "^1.0.1",
					"gopd": "^1.2.0",
					"has-symbols": "^1.1.0",
					"hasown": "^2.0.2",
					"math-intrinsics": "^1.1.0",
				},
			},
			"sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==",
		],

		"get-proto": [
			"get-proto@1.0.1",
			"",
			{ "dependencies": { "dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0" } },
			"sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==",
		],

		"glob-parent": [
			"glob-parent@6.0.2",
			"",
			{ "dependencies": { "is-glob": "^4.0.3" } },
			"sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",
		],

		"globals": [
			"globals@16.3.0",
			"",
			{},
			"sha512-bqWEnJ1Nt3neqx2q5SFfGS8r/ahumIakg3HcwtNlrVlwXIeNumWn/c7Pn/wKzGhf6SaW6H6uWXLqC30STCMchQ==",
		],

		"gopd": ["gopd@1.2.0", "", {}, "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="],

		"graphql": [
			"graphql@16.11.0",
			"",
			{},
			"sha512-mS1lbMsxgQj6hge1XZ6p7GPhbrtFwUFYi3wRzXAC/FmYnyXMTvvI3td3rjmQ2u8ewXueaSvRPWaEcgVVOT9Jnw==",
		],

		"graphql-request": [
			"graphql-request@6.1.0",
			"",
			{
				"dependencies": { "@graphql-typed-document-node/core": "^3.2.0", "cross-fetch": "^3.1.5" },
				"peerDependencies": { "graphql": "14 - 16" },
			},
			"sha512-p+XPfS4q7aIpKVcgmnZKhMNqhltk20hfXtkaIkTfjjmiKMJ5xrt5c743cL03y/K7y1rg3WrIC49xGiEQ4mxdNw==",
		],

		"has-flag": [
			"has-flag@4.0.0",
			"",
			{},
			"sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==",
		],

		"has-symbols": [
			"has-symbols@1.1.0",
			"",
			{},
			"sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==",
		],

		"has-tostringtag": [
			"has-tostringtag@1.0.2",
			"",
			{ "dependencies": { "has-symbols": "^1.0.3" } },
			"sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==",
		],

		"hasown": [
			"hasown@2.0.2",
			"",
			{ "dependencies": { "function-bind": "^1.1.2" } },
			"sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
		],

		"hono": ["hono@4.9.2", "", {}, "sha512-UG2jXGS/gkLH42l/1uROnwXpkjvvxkl3kpopL3LBo27NuaDPI6xHNfuUSilIHcrBkPfl4y0z6y2ByI455TjNRw=="],

		"humanize-ms": [
			"humanize-ms@1.2.1",
			"",
			{ "dependencies": { "ms": "^2.0.0" } },
			"sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==",
		],

		"ibm-cloud-sdk-core": [
			"ibm-cloud-sdk-core@5.4.2",
			"",
			{
				"dependencies": {
					"@types/debug": "^4.1.12",
					"@types/node": "^18.19.80",
					"@types/tough-cookie": "^4.0.0",
					"axios": "^1.11.0",
					"camelcase": "^6.3.0",
					"debug": "^4.3.4",
					"dotenv": "^16.4.5",
					"extend": "3.0.2",
					"file-type": "16.5.4",
					"form-data": "^4.0.4",
					"isstream": "0.1.2",
					"jsonwebtoken": "^9.0.2",
					"mime-types": "2.1.35",
					"retry-axios": "^2.6.0",
					"tough-cookie": "^4.1.3",
				},
			},
			"sha512-5VFkKYU/vSIWFJTVt392XEdPmiEwUJqhxjn1MRO3lfELyU2FB+yYi8brbmXUgq+D1acHR1fpS7tIJ6IlnrR9Cg==",
		],

		"ieee754": [
			"ieee754@1.2.1",
			"",
			{},
			"sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==",
		],

		"ignore": [
			"ignore@5.3.2",
			"",
			{},
			"sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==",
		],

		"import-fresh": [
			"import-fresh@3.3.1",
			"",
			{ "dependencies": { "parent-module": "^1.0.0", "resolve-from": "^4.0.0" } },
			"sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==",
		],

		"imurmurhash": [
			"imurmurhash@0.1.4",
			"",
			{},
			"sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==",
		],

		"is-extglob": [
			"is-extglob@2.1.1",
			"",
			{},
			"sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
		],

		"is-fullwidth-code-point": [
			"is-fullwidth-code-point@3.0.0",
			"",
			{},
			"sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
		],

		"is-glob": [
			"is-glob@4.0.3",
			"",
			{ "dependencies": { "is-extglob": "^2.1.1" } },
			"sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
		],

		"isexe": ["isexe@2.0.0", "", {}, "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="],

		"isstream": [
			"isstream@0.1.2",
			"",
			{},
			"sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==",
		],

		"js-tiktoken": [
			"js-tiktoken@1.0.20",
			"",
			{ "dependencies": { "base64-js": "^1.5.1" } },
			"sha512-Xlaqhhs8VfCd6Sh7a1cFkZHQbYTLCwVJJWiHVxBYzLPxW0XsoxBy1hitmjkdIjD3Aon5BXLHFwU5O8WUx6HH+A==",
		],

		"js-yaml": [
			"js-yaml@4.1.0",
			"",
			{ "dependencies": { "argparse": "^2.0.1" }, "bin": "bin/js-yaml.js" },
			"sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",
		],

		"json-buffer": [
			"json-buffer@3.0.1",
			"",
			{},
			"sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==",
		],

		"json-schema-traverse": [
			"json-schema-traverse@0.4.1",
			"",
			{},
			"sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==",
		],

		"json-stable-stringify-without-jsonify": [
			"json-stable-stringify-without-jsonify@1.0.1",
			"",
			{},
			"sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==",
		],

		"jsonpointer": [
			"jsonpointer@5.0.1",
			"",
			{},
			"sha512-p/nXbhSEcu3pZRdkW1OfJhpsVtW1gd4Wa1fnQc9YLiTfAjn0312eMKimbdIQzuZl9aa9xUGaRlP9T/CJE/ditQ==",
		],

		"jsonwebtoken": [
			"jsonwebtoken@9.0.2",
			"",
			{
				"dependencies": {
					"jws": "^3.2.2",
					"lodash.includes": "^4.3.0",
					"lodash.isboolean": "^3.0.3",
					"lodash.isinteger": "^4.0.4",
					"lodash.isnumber": "^3.0.3",
					"lodash.isplainobject": "^4.0.6",
					"lodash.isstring": "^4.0.1",
					"lodash.once": "^4.0.0",
					"ms": "^2.1.1",
					"semver": "^7.5.4",
				},
			},
			"sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==",
		],

		"jwa": [
			"jwa@1.4.2",
			"",
			{ "dependencies": { "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1" } },
			"sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==",
		],

		"jws": [
			"jws@3.2.2",
			"",
			{ "dependencies": { "jwa": "^1.4.1", "safe-buffer": "^5.0.1" } },
			"sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==",
		],

		"keyv": [
			"keyv@4.5.4",
			"",
			{ "dependencies": { "json-buffer": "3.0.1" } },
			"sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==",
		],

		"langchain": [
			"langchain@0.3.30",
			"",
			{
				"dependencies": {
					"@langchain/openai": ">=0.1.0 <0.7.0",
					"@langchain/textsplitters": ">=0.0.0 <0.2.0",
					"js-tiktoken": "^1.0.12",
					"js-yaml": "^4.1.0",
					"jsonpointer": "^5.0.1",
					"langsmith": "^0.3.33",
					"openapi-types": "^12.1.3",
					"p-retry": "4",
					"uuid": "^10.0.0",
					"yaml": "^2.2.1",
					"zod": "^3.25.32",
				},
				"peerDependencies": {
					"@langchain/anthropic": "*",
					"@langchain/aws": "*",
					"@langchain/cerebras": "*",
					"@langchain/cohere": "*",
					"@langchain/core": ">=0.3.58 <0.4.0",
					"@langchain/deepseek": "*",
					"@langchain/google-genai": "*",
					"@langchain/google-vertexai": "*",
					"@langchain/google-vertexai-web": "*",
					"@langchain/groq": "*",
					"@langchain/mistralai": "*",
					"@langchain/ollama": "*",
					"@langchain/xai": "*",
					"axios": "*",
					"cheerio": "*",
					"handlebars": "^4.7.8",
					"peggy": "^3.0.2",
					"typeorm": "*",
				},
				"optionalPeers": [
					"@langchain/anthropic",
					"@langchain/aws",
					"@langchain/cerebras",
					"@langchain/cohere",
					"@langchain/deepseek",
					"@langchain/google-vertexai",
					"@langchain/google-vertexai-web",
					"@langchain/groq",
					"@langchain/mistralai",
					"@langchain/ollama",
					"@langchain/xai",
					"cheerio",
					"handlebars",
					"peggy",
					"typeorm",
				],
			},
			"sha512-UyVsfwHDpHbrnWrjWuhJHqi8Non+Zcsf2kdpDTqyJF8NXrHBOpjdHT5LvPuW9fnE7miDTWf5mLcrWAGZgcrznQ==",
		],

		"langfuse": [
			"langfuse@3.38.4",
			"",
			{ "dependencies": { "langfuse-core": "^3.38.4" } },
			"sha512-2UqMeHLl3DGNX1Nh/cO4jGhk7TzDJ6gjQLlyS9rwFCKVO81xot6b58yeTsTB5YrWupWsOxQtMNoQYIQGOUlH9Q==",
		],

		"langfuse-core": [
			"langfuse-core@3.38.4",
			"",
			{ "dependencies": { "mustache": "^4.2.0" } },
			"sha512-onTAqcEGhoXuBgqDFXe2t+bt9Vi+5YChRgdz3voM49JKoHwtVZQiUdqTfjSivGR75eSbYoiaIL8IRoio+jaqwg==",
		],

		"langfuse-langchain": [
			"langfuse-langchain@3.38.4",
			"",
			{
				"dependencies": { "langfuse": "^3.38.4", "langfuse-core": "^3.38.4" },
				"peerDependencies": { "langchain": ">=0.0.157 <0.4.0" },
			},
			"sha512-7HJqouMrVOP9MFdu33M4G4uBFyQAIh/DqGYALfs41xqm7t99eZxKcTvt4rYZy67iQAhd58TG3q8+9haGzuLbOA==",
		],

		"langsmith": [
			"langsmith@0.3.56",
			"",
			{
				"dependencies": {
					"@types/uuid": "^10.0.0",
					"chalk": "^4.1.2",
					"console-table-printer": "^2.12.1",
					"p-queue": "^6.6.2",
					"p-retry": "4",
					"semver": "^7.6.3",
					"uuid": "^10.0.0",
				},
				"peerDependencies": {
					"@opentelemetry/api": "*",
					"@opentelemetry/exporter-trace-otlp-proto": "*",
					"@opentelemetry/sdk-trace-base": "*",
					"openai": "*",
				},
				"optionalPeers": ["@opentelemetry/api", "@opentelemetry/exporter-trace-otlp-proto", "@opentelemetry/sdk-trace-base"],
			},
			"sha512-LLAppYZ6EWodnWSqgf/526ONtfb4Z1qR3QnF1+mYgOAr7aPOs9KNvxZMoCsg4oTXfxgf/+UJQ3Q1roTQEKJUag==",
		],

		"levn": [
			"levn@0.4.1",
			"",
			{ "dependencies": { "prelude-ls": "^1.2.1", "type-check": "~0.4.0" } },
			"sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==",
		],

		"locate-path": [
			"locate-path@6.0.0",
			"",
			{ "dependencies": { "p-locate": "^5.0.0" } },
			"sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==",
		],

		"lodash.camelcase": [
			"lodash.camelcase@4.3.0",
			"",
			{},
			"sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==",
		],

		"lodash.includes": [
			"lodash.includes@4.3.0",
			"",
			{},
			"sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==",
		],

		"lodash.isboolean": [
			"lodash.isboolean@3.0.3",
			"",
			{},
			"sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==",
		],

		"lodash.isinteger": [
			"lodash.isinteger@4.0.4",
			"",
			{},
			"sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==",
		],

		"lodash.isnumber": [
			"lodash.isnumber@3.0.3",
			"",
			{},
			"sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==",
		],

		"lodash.isplainobject": [
			"lodash.isplainobject@4.0.6",
			"",
			{},
			"sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==",
		],

		"lodash.isstring": [
			"lodash.isstring@4.0.1",
			"",
			{},
			"sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==",
		],

		"lodash.merge": [
			"lodash.merge@4.6.2",
			"",
			{},
			"sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==",
		],

		"lodash.once": [
			"lodash.once@4.1.1",
			"",
			{},
			"sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==",
		],

		"long": ["long@5.3.2", "", {}, "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA=="],

		"math-intrinsics": [
			"math-intrinsics@1.1.0",
			"",
			{},
			"sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==",
		],

		"mime-db": [
			"mime-db@1.52.0",
			"",
			{},
			"sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==",
		],

		"mime-types": [
			"mime-types@2.1.35",
			"",
			{ "dependencies": { "mime-db": "1.52.0" } },
			"sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==",
		],

		"minimatch": [
			"minimatch@3.1.2",
			"",
			{ "dependencies": { "brace-expansion": "^1.1.7" } },
			"sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",
		],

		"ms": ["ms@2.1.3", "", {}, "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="],

		"mustache": [
			"mustache@4.2.0",
			"",
			{ "bin": "bin/mustache" },
			"sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==",
		],

		"nanoid": [
			"nanoid@5.1.5",
			"",
			{ "bin": "bin/nanoid.js" },
			"sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==",
		],

		"natural-compare": [
			"natural-compare@1.4.0",
			"",
			{},
			"sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==",
		],

		"nice-grpc": [
			"nice-grpc@2.1.12",
			"",
			{ "dependencies": { "@grpc/grpc-js": "^1.13.1", "abort-controller-x": "^0.4.0", "nice-grpc-common": "^2.0.2" } },
			"sha512-J1n4Wg+D3IhRhGQb+iqh2OpiM0GzTve/kf2lnlW4S+xczmIEd0aHUDV1OsJ5a3q8GSTqJf+s4Rgg1M8uJltarw==",
		],

		"nice-grpc-client-middleware-retry": [
			"nice-grpc-client-middleware-retry@3.1.11",
			"",
			{ "dependencies": { "abort-controller-x": "^0.4.0", "nice-grpc-common": "^2.0.2" } },
			"sha512-xW/imz/kNG2g0DwTfH2eYEGrg1chSLrXtvGp9fg2qkhTgGFfAS/Pq3+t+9G8KThcC4hK/xlEyKvZWKk++33S6A==",
		],

		"nice-grpc-common": [
			"nice-grpc-common@2.0.2",
			"",
			{ "dependencies": { "ts-error": "^1.0.6" } },
			"sha512-7RNWbls5kAL1QVUOXvBsv1uO0wPQK3lHv+cY1gwkTzirnG1Nop4cBJZubpgziNbaVc/bl9QJcyvsf/NQxa3rjQ==",
		],

		"node-domexception": [
			"node-domexception@1.0.0",
			"",
			{},
			"sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==",
		],

		"node-fetch": [
			"node-fetch@2.7.0",
			"",
			{ "dependencies": { "whatwg-url": "^5.0.0" }, "peerDependencies": { "encoding": "^0.1.0" }, "optionalPeers": ["encoding"] },
			"sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
		],

		"openai": [
			"openai@4.104.0",
			"",
			{
				"dependencies": {
					"@types/node": "^18.11.18",
					"@types/node-fetch": "^2.6.4",
					"abort-controller": "^3.0.0",
					"agentkeepalive": "^4.2.1",
					"form-data-encoder": "1.7.2",
					"formdata-node": "^4.3.2",
					"node-fetch": "^2.6.7",
				},
				"peerDependencies": { "ws": "^8.18.0", "zod": "^3.23.8" },
				"bin": "bin/cli",
			},
			"sha512-p99EFNsA/yX6UhVO93f5kJsDRLAg+CTA2RBqdHK4RtK8u5IJw32Hyb2dTGKbnnFmnuoBv5r7Z2CURI9sGZpSuA==",
		],

		"openapi-types": [
			"openapi-types@12.1.3",
			"",
			{},
			"sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==",
		],

		"optionator": [
			"optionator@0.9.4",
			"",
			{
				"dependencies": {
					"deep-is": "^0.1.3",
					"fast-levenshtein": "^2.0.6",
					"levn": "^0.4.1",
					"prelude-ls": "^1.2.1",
					"type-check": "^0.4.0",
					"word-wrap": "^1.2.5",
				},
			},
			"sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==",
		],

		"p-finally": [
			"p-finally@1.0.0",
			"",
			{},
			"sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==",
		],

		"p-limit": [
			"p-limit@3.1.0",
			"",
			{ "dependencies": { "yocto-queue": "^0.1.0" } },
			"sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",
		],

		"p-locate": [
			"p-locate@5.0.0",
			"",
			{ "dependencies": { "p-limit": "^3.0.2" } },
			"sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==",
		],

		"p-queue": [
			"p-queue@6.6.2",
			"",
			{ "dependencies": { "eventemitter3": "^4.0.4", "p-timeout": "^3.2.0" } },
			"sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==",
		],

		"p-retry": [
			"p-retry@4.6.2",
			"",
			{ "dependencies": { "@types/retry": "0.12.0", "retry": "^0.13.1" } },
			"sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==",
		],

		"p-timeout": [
			"p-timeout@3.2.0",
			"",
			{ "dependencies": { "p-finally": "^1.0.0" } },
			"sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==",
		],

		"parent-module": [
			"parent-module@1.0.1",
			"",
			{ "dependencies": { "callsites": "^3.0.0" } },
			"sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==",
		],

		"path-exists": [
			"path-exists@4.0.0",
			"",
			{},
			"sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",
		],

		"path-key": [
			"path-key@3.1.1",
			"",
			{},
			"sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
		],

		"peek-readable": [
			"peek-readable@4.1.0",
			"",
			{},
			"sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==",
		],

		"pg": [
			"pg@8.16.3",
			"",
			{
				"dependencies": {
					"pg-connection-string": "^2.9.1",
					"pg-pool": "^3.10.1",
					"pg-protocol": "^1.10.3",
					"pg-types": "2.2.0",
					"pgpass": "1.0.5",
				},
				"optionalDependencies": { "pg-cloudflare": "^1.2.7" },
				"peerDependencies": { "pg-native": ">=3.0.1" },
				"optionalPeers": ["pg-native"],
			},
			"sha512-enxc1h0jA/aq5oSDMvqyW3q89ra6XIIDZgCX9vkMrnz5DFTw/Ny3Li2lFQ+pt3L6MCgm/5o2o8HW9hiJji+xvw==",
		],

		"pg-cloudflare": [
			"pg-cloudflare@1.2.7",
			"",
			{},
			"sha512-YgCtzMH0ptvZJslLM1ffsY4EuGaU0cx4XSdXLRFae8bPP4dS5xL1tNB3k2o/N64cHJpwU7dxKli/nZ2lUa5fLg==",
		],

		"pg-connection-string": [
			"pg-connection-string@2.9.1",
			"",
			{},
			"sha512-nkc6NpDcvPVpZXxrreI/FOtX3XemeLl8E0qFr6F2Lrm/I8WOnaWNhIPK2Z7OHpw7gh5XJThi6j6ppgNoaT1w4w==",
		],

		"pg-int8": [
			"pg-int8@1.0.1",
			"",
			{},
			"sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==",
		],

		"pg-pool": [
			"pg-pool@3.10.1",
			"",
			{ "peerDependencies": { "pg": ">=8.0" } },
			"sha512-Tu8jMlcX+9d8+QVzKIvM/uJtp07PKr82IUOYEphaWcoBhIYkoHpLXN3qO59nAI11ripznDsEzEv8nUxBVWajGg==",
		],

		"pg-protocol": [
			"pg-protocol@1.10.3",
			"",
			{},
			"sha512-6DIBgBQaTKDJyxnXaLiLR8wBpQQcGWuAESkRBX/t6OwA8YsqP+iVSiond2EDy6Y/dsGk8rh/jtax3js5NeV7JQ==",
		],

		"pg-types": [
			"pg-types@2.2.0",
			"",
			{
				"dependencies": {
					"pg-int8": "1.0.1",
					"postgres-array": "~2.0.0",
					"postgres-bytea": "~1.0.0",
					"postgres-date": "~1.0.4",
					"postgres-interval": "^1.1.0",
				},
			},
			"sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==",
		],

		"pgpass": [
			"pgpass@1.0.5",
			"",
			{ "dependencies": { "split2": "^4.1.0" } },
			"sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==",
		],

		"playwright": [
			"playwright@1.54.2",
			"",
			{ "dependencies": { "playwright-core": "1.54.2" }, "optionalDependencies": { "fsevents": "2.3.2" }, "bin": "cli.js" },
			"sha512-Hu/BMoA1NAdRUuulyvQC0pEqZ4vQbGfn8f7wPXcnqQmM+zct9UliKxsIkLNmz/ku7LElUNqmaiv1TG/aL5ACsw==",
		],

		"playwright-core": [
			"playwright-core@1.54.2",
			"",
			{ "bin": "cli.js" },
			"sha512-n5r4HFbMmWsB4twG7tJLDN9gmBUeSPcsBZiWSE4DnYz9mJMAFqr2ID7+eGC9kpEnxExJ1epttwR59LEWCk8mtA==",
		],

		"postgres-array": [
			"postgres-array@2.0.0",
			"",
			{},
			"sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==",
		],

		"postgres-bytea": [
			"postgres-bytea@1.0.0",
			"",
			{},
			"sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==",
		],

		"postgres-date": [
			"postgres-date@1.0.7",
			"",
			{},
			"sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==",
		],

		"postgres-interval": [
			"postgres-interval@1.2.0",
			"",
			{ "dependencies": { "xtend": "^4.0.0" } },
			"sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==",
		],

		"prelude-ls": [
			"prelude-ls@1.2.1",
			"",
			{},
			"sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==",
		],

		"prettier": [
			"prettier@3.6.2",
			"",
			{ "bin": "bin/prettier.cjs" },
			"sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==",
		],

		"prettier-plugin-organize-imports": [
			"prettier-plugin-organize-imports@4.2.0",
			"",
			{ "peerDependencies": { "prettier": ">=2.0", "typescript": ">=2.9", "vue-tsc": "^2.1.0 || 3" }, "optionalPeers": ["vue-tsc"] },
			"sha512-Zdy27UhlmyvATZi67BTnLcKTo8fm6Oik59Sz6H64PgZJVs6NJpPD1mT240mmJn62c98/QaL+r3kx9Q3gRpDajg==",
		],

		"process": [
			"process@0.11.10",
			"",
			{},
			"sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==",
		],

		"protobufjs": [
			"protobufjs@7.5.3",
			"",
			{
				"dependencies": {
					"@protobufjs/aspromise": "^1.1.2",
					"@protobufjs/base64": "^1.1.2",
					"@protobufjs/codegen": "^2.0.4",
					"@protobufjs/eventemitter": "^1.1.0",
					"@protobufjs/fetch": "^1.1.0",
					"@protobufjs/float": "^1.0.2",
					"@protobufjs/inquire": "^1.1.0",
					"@protobufjs/path": "^1.1.2",
					"@protobufjs/pool": "^1.1.0",
					"@protobufjs/utf8": "^1.1.0",
					"@types/node": ">=13.7.0",
					"long": "^5.0.0",
				},
			},
			"sha512-sildjKwVqOI2kmFDiXQ6aEB0fjYTafpEvIBs8tOR8qI4spuL9OPROLVu2qZqi/xgCfsHIwVqlaF8JBjWFHnKbw==",
		],

		"proxy-from-env": [
			"proxy-from-env@1.1.0",
			"",
			{},
			"sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==",
		],

		"psl": [
			"psl@1.15.0",
			"",
			{ "dependencies": { "punycode": "^2.3.1" } },
			"sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==",
		],

		"punycode": [
			"punycode@2.3.1",
			"",
			{},
			"sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",
		],

		"querystringify": [
			"querystringify@2.2.0",
			"",
			{},
			"sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==",
		],

		"readable-stream": [
			"readable-stream@4.7.0",
			"",
			{
				"dependencies": {
					"abort-controller": "^3.0.0",
					"buffer": "^6.0.3",
					"events": "^3.3.0",
					"process": "^0.11.10",
					"string_decoder": "^1.3.0",
				},
			},
			"sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==",
		],

		"readable-web-to-node-stream": [
			"readable-web-to-node-stream@3.0.4",
			"",
			{ "dependencies": { "readable-stream": "^4.7.0" } },
			"sha512-9nX56alTf5bwXQ3ZDipHJhusu9NTQJ/CVPtb/XHAJCXihZeitfJvIRS4GqQ/mfIoOE3IelHMrpayVrosdHBuLw==",
		],

		"require-directory": [
			"require-directory@2.1.1",
			"",
			{},
			"sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==",
		],

		"requires-port": [
			"requires-port@1.0.0",
			"",
			{},
			"sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==",
		],

		"resolve-from": [
			"resolve-from@4.0.0",
			"",
			{},
			"sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==",
		],

		"retry": [
			"retry@0.13.1",
			"",
			{},
			"sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==",
		],

		"retry-axios": [
			"retry-axios@2.6.0",
			"",
			{ "peerDependencies": { "axios": "*" } },
			"sha512-pOLi+Gdll3JekwuFjXO3fTq+L9lzMQGcSq7M5gIjExcl3Gu1hd4XXuf5o3+LuSBsaULQH7DiNbsqPd1chVpQGQ==",
		],

		"safe-buffer": [
			"safe-buffer@5.2.1",
			"",
			{},
			"sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==",
		],

		"semver": [
			"semver@7.6.3",
			"",
			{ "bin": "bin/semver.js" },
			"sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==",
		],

		"shebang-command": [
			"shebang-command@2.0.0",
			"",
			{ "dependencies": { "shebang-regex": "^3.0.0" } },
			"sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",
		],

		"shebang-regex": [
			"shebang-regex@3.0.0",
			"",
			{},
			"sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",
		],

		"simple-wcswidth": [
			"simple-wcswidth@1.0.1",
			"",
			{},
			"sha512-xMO/8eNREtaROt7tJvWJqHBDTMFN4eiQ5I4JRMuilwfnFcV5W9u7RUkueNkdw0jPqGMX36iCywelS5yilTuOxg==",
		],

		"split2": [
			"split2@4.2.0",
			"",
			{},
			"sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==",
		],

		"string-width": [
			"string-width@4.2.3",
			"",
			{ "dependencies": { "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1" } },
			"sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
		],

		"string_decoder": [
			"string_decoder@1.3.0",
			"",
			{ "dependencies": { "safe-buffer": "~5.2.0" } },
			"sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==",
		],

		"strip-ansi": [
			"strip-ansi@6.0.1",
			"",
			{ "dependencies": { "ansi-regex": "^5.0.1" } },
			"sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
		],

		"strip-json-comments": [
			"strip-json-comments@3.1.1",
			"",
			{},
			"sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==",
		],

		"strtok3": [
			"strtok3@6.3.0",
			"",
			{ "dependencies": { "@tokenizer/token": "^0.3.0", "peek-readable": "^4.1.0" } },
			"sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==",
		],

		"supports-color": [
			"supports-color@7.2.0",
			"",
			{ "dependencies": { "has-flag": "^4.0.0" } },
			"sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==",
		],

		"token-types": [
			"token-types@4.2.1",
			"",
			{ "dependencies": { "@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1" } },
			"sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==",
		],

		"tough-cookie": [
			"tough-cookie@4.1.4",
			"",
			{ "dependencies": { "psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3" } },
			"sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==",
		],

		"tr46": ["tr46@0.0.3", "", {}, "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="],

		"ts-error": [
			"ts-error@1.0.6",
			"",
			{},
			"sha512-tLJxacIQUM82IR7JO1UUkKlYuUTmoY9HBJAmNWFzheSlDS5SPMcNIepejHJa4BpPQLAcbRhRf3GDJzyj6rbKvA==",
		],

		"type-check": [
			"type-check@0.4.0",
			"",
			{ "dependencies": { "prelude-ls": "^1.2.1" } },
			"sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==",
		],

		"typescript": [
			"typescript@5.9.2",
			"",
			{ "bin": { "tsc": "bin/tsc", "tsserver": "bin/tsserver" } },
			"sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==",
		],

		"uncrypto": [
			"uncrypto@0.1.3",
			"",
			{},
			"sha512-Ql87qFHB3s/De2ClA9e0gsnS6zXG27SkTiSJwjCc9MebbfapQfuPzumMIUMi38ezPZVNFcHI9sUIepeQfw8J8Q==",
		],

		"undici-types": [
			"undici-types@5.26.5",
			"",
			{},
			"sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
		],

		"universalify": [
			"universalify@0.2.0",
			"",
			{},
			"sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==",
		],

		"uri-js": [
			"uri-js@4.4.1",
			"",
			{ "dependencies": { "punycode": "^2.1.0" } },
			"sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==",
		],

		"url-parse": [
			"url-parse@1.5.10",
			"",
			{ "dependencies": { "querystringify": "^2.1.1", "requires-port": "^1.0.0" } },
			"sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==",
		],

		"uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"weaviate-client": [
			"weaviate-client@3.8.0",
			"",
			{
				"dependencies": {
					"abort-controller-x": "^0.4.3",
					"graphql": "^16.11.0",
					"graphql-request": "^6.1.0",
					"long": "^5.3.2",
					"nice-grpc": "^2.1.12",
					"nice-grpc-client-middleware-retry": "^3.1.11",
					"nice-grpc-common": "^2.0.2",
					"uuid": "^9.0.1",
				},
			},
			"sha512-8yWNY3OIAh1H/W+414o17em/3CSDc5f4/sMjCrFksVppczHC1mB1f0fCFosg9fpQGegLT+Ll6un82sTGYxFhqw==",
		],

		"web-streams-polyfill": [
			"web-streams-polyfill@4.0.0-beta.3",
			"",
			{},
			"sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==",
		],

		"webidl-conversions": [
			"webidl-conversions@3.0.1",
			"",
			{},
			"sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==",
		],

		"whatwg-url": [
			"whatwg-url@5.0.0",
			"",
			{ "dependencies": { "tr46": "~0.0.3", "webidl-conversions": "^3.0.0" } },
			"sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==",
		],

		"which": [
			"which@2.0.2",
			"",
			{ "dependencies": { "isexe": "^2.0.0" }, "bin": { "node-which": "bin/node-which" } },
			"sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",
		],

		"word-wrap": [
			"word-wrap@1.2.5",
			"",
			{},
			"sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==",
		],

		"wrap-ansi": [
			"wrap-ansi@7.0.0",
			"",
			{ "dependencies": { "ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0" } },
			"sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
		],

		"ws": [
			"ws@8.18.0",
			"",
			{
				"peerDependencies": { "bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2" },
				"optionalPeers": ["bufferutil", "utf-8-validate"],
			},
			"sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==",
		],

		"xtend": ["xtend@4.0.2", "", {}, "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="],

		"y18n": ["y18n@5.0.8", "", {}, "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="],

		"yaml": [
			"yaml@2.8.0",
			"",
			{ "bin": "bin.mjs" },
			"sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==",
		],

		"yargs": [
			"yargs@17.7.2",
			"",
			{
				"dependencies": {
					"cliui": "^8.0.1",
					"escalade": "^3.1.1",
					"get-caller-file": "^2.0.5",
					"require-directory": "^2.1.1",
					"string-width": "^4.2.3",
					"y18n": "^5.0.5",
					"yargs-parser": "^21.1.1",
				},
			},
			"sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==",
		],

		"yargs-parser": [
			"yargs-parser@21.1.1",
			"",
			{},
			"sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==",
		],

		"yocto-queue": [
			"yocto-queue@0.1.0",
			"",
			{},
			"sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",
		],

		"zod": ["zod@3.25.63", "", {}, "sha512-3ttCkqhtpncYXfP0f6dsyabbYV/nEUW+Xlu89jiXbTBifUfjaSqXOG6JnQPLtqt87n7KAmnMqcjay6c0Wq0Vbw=="],

		"zod-to-json-schema": [
			"zod-to-json-schema@3.24.5",
			"",
			{ "peerDependencies": { "zod": "^3.24.1" } },
			"sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==",
		],

		"@anthropic-ai/sdk/@types/node": [
			"@types/node@18.19.122",
			"",
			{ "dependencies": { "undici-types": "~5.26.4" } },
			"sha512-yzegtT82dwTNEe/9y+CM8cgb42WrUfMMCg2QqSddzO1J6uPmBD7qKCZ7dOHZP2Yrpm/kb0eqdNMn2MUyEiqBmA==",
		],

		"@eslint-community/eslint-utils/eslint-visitor-keys": [
			"eslint-visitor-keys@3.4.3",
			"",
			{},
			"sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==",
		],

		"@eslint/eslintrc/globals": [
			"globals@14.0.0",
			"",
			{},
			"sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==",
		],

		"@humanfs/node/@humanwhocodes/retry": [
			"@humanwhocodes/retry@0.3.1",
			"",
			{},
			"sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==",
		],

		"@ibm-cloud/watsonx-ai/@types/node": [
			"@types/node@18.19.122",
			"",
			{ "dependencies": { "undici-types": "~5.26.4" } },
			"sha512-yzegtT82dwTNEe/9y+CM8cgb42WrUfMMCg2QqSddzO1J6uPmBD7qKCZ7dOHZP2Yrpm/kb0eqdNMn2MUyEiqBmA==",
		],

		"@langchain/core/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"@langchain/google-common/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"@langchain/google-genai/uuid": [
			"uuid@11.1.0",
			"",
			{ "bin": "dist/esm/bin/uuid" },
			"sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==",
		],

		"@langchain/langgraph/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"@langchain/langgraph-checkpoint/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"@langchain/langgraph-sdk/uuid": [
			"uuid@9.0.1",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
		],

		"@langchain/openai/openai": [
			"openai@5.12.2",
			"",
			{ "peerDependencies": { "ws": "^8.18.0", "zod": "^3.23.8" }, "bin": "bin/cli" },
			"sha512-xqzHHQch5Tws5PcKR2xsZGX9xtch+JQFz5zb14dGqlshmmDAFBFEWmeIpf7wVqWV+w7Emj7jRgkNJakyKE0tYQ==",
		],

		"@langchain/weaviate/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"@types/node-fetch/@types/node": [
			"@types/node@24.0.6",
			"",
			{ "dependencies": { "undici-types": "~7.8.0" } },
			"sha512-ZOyn+gOs749xU7ovp+Ibj0g1o3dFRqsfPnT22C2t5JzcRvgsEDpGawPbCISGKLudJk9Y0wiu9sYd6kUh0pc9TA==",
		],

		"chalk/ansi-styles": [
			"ansi-styles@4.3.0",
			"",
			{ "dependencies": { "color-convert": "^2.0.1" } },
			"sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
		],

		"exa-js/dotenv": [
			"dotenv@16.4.7",
			"",
			{},
			"sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==",
		],

		"exa-js/openai": [
			"openai@5.12.2",
			"",
			{ "peerDependencies": { "ws": "^8.18.0", "zod": "^3.23.8" }, "bin": "bin/cli" },
			"sha512-xqzHHQch5Tws5PcKR2xsZGX9xtch+JQFz5zb14dGqlshmmDAFBFEWmeIpf7wVqWV+w7Emj7jRgkNJakyKE0tYQ==",
		],

		"form-data/mime-types": [
			"mime-types@2.1.35",
			"",
			{ "dependencies": { "mime-db": "1.52.0" } },
			"sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==",
		],

		"graphql-request/cross-fetch": [
			"cross-fetch@3.2.0",
			"",
			{ "dependencies": { "node-fetch": "^2.7.0" } },
			"sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==",
		],

		"ibm-cloud-sdk-core/@types/node": [
			"@types/node@18.19.122",
			"",
			{ "dependencies": { "undici-types": "~5.26.4" } },
			"sha512-yzegtT82dwTNEe/9y+CM8cgb42WrUfMMCg2QqSddzO1J6uPmBD7qKCZ7dOHZP2Yrpm/kb0eqdNMn2MUyEiqBmA==",
		],

		"langchain/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"langsmith/uuid": [
			"uuid@10.0.0",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
		],

		"openai/@types/node": [
			"@types/node@18.19.115",
			"",
			{ "dependencies": { "undici-types": "~5.26.4" } },
			"sha512-kNrFiTgG4a9JAn1LMQeLOv3MvXIPokzXziohMrMsvpYgLpdEt/mMiVYc4sGKtDfyxM5gIDF4VgrPRyCw4fHOYg==",
		],

		"protobufjs/@types/node": [
			"@types/node@24.0.6",
			"",
			{ "dependencies": { "undici-types": "~7.8.0" } },
			"sha512-ZOyn+gOs749xU7ovp+Ibj0g1o3dFRqsfPnT22C2t5JzcRvgsEDpGawPbCISGKLudJk9Y0wiu9sYd6kUh0pc9TA==",
		],

		"weaviate-client/uuid": [
			"uuid@9.0.1",
			"",
			{ "bin": "dist/bin/uuid" },
			"sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
		],

		"wrap-ansi/ansi-styles": [
			"ansi-styles@4.3.0",
			"",
			{ "dependencies": { "color-convert": "^2.0.1" } },
			"sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
		],

		"@anthropic-ai/sdk/@types/node/undici-types": [
			"undici-types@5.26.5",
			"",
			{},
			"sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
		],

		"@ibm-cloud/watsonx-ai/@types/node/undici-types": [
			"undici-types@5.26.5",
			"",
			{},
			"sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
		],

		"@types/node-fetch/@types/node/undici-types": [
			"undici-types@7.8.0",
			"",
			{},
			"sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==",
		],

		"form-data/mime-types/mime-db": [
			"mime-db@1.52.0",
			"",
			{},
			"sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==",
		],

		"ibm-cloud-sdk-core/@types/node/undici-types": [
			"undici-types@5.26.5",
			"",
			{},
			"sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
		],

		"openai/@types/node/undici-types": [
			"undici-types@5.26.5",
			"",
			{},
			"sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
		],

		"protobufjs/@types/node/undici-types": [
			"undici-types@7.8.0",
			"",
			{},
			"sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==",
		],
	},
}
