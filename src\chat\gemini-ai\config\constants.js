/**
 * @fileoverview Constants and configuration values for the GeminiAI module.
 */

// Model configurations
export const DEFAULT_GEMINI_MODELS = 'gemini-2.5-flash,gemini-2.5-flash-lite,gemini-2.5-pro';
export const GROQ_MODELS = 'openai/gpt-oss-120b,moonshotai/kimi-k2-instruct,qwen/qwen3-32b';
export const CEREBRAS_MODELS =
	'qwen-3-235b-a22b-instruct-2507,gpt-oss-120b,qwen-3-coder-480b,qwen-3-235b-a22b-thinking-2507,qwen-3-32b,llama-3.3-70b';
export const OPENAI_MODELS = 'gpt-4o,gpt-4o-mini';

// Temperature settings
export const DEFAULT_TEMPERATURE = 1;
export const DEFAULT_FAST_TEMPERATURE = 0.5;

// Response configuration
export const DEFAULT_RESPONSE_MIME_TYPE = 'text/plain';
export const DEFAULT_THINKING_BUDGET = 0;

// Embedding configuration
export const EMBEDDING_MODEL = 'text-embedding-004';

// API endpoints
export const GROQ_BASE_URL = 'https://api.groq.com/openai/v1';
export const CEREBRAS_BASE_URL = 'https://api.cerebras.ai/v1';
export const OPENAI_BASE_URL = 'https://api.openai.com/v1';
