/**
 * Splits a given text into chunks that do not exceed a specified maximum length,
 * attempting to split at natural word breaks where possible.
 * This function handles messages that exceed Telegram's length limits by intelligently
 * splitting text at logical boundaries.
 *
 * The algorithm prioritizes splitting by:
 * 1. Lines (preserving paragraph structure)
 * 2. Words (at spaces, avoiding mid-word breaks)
 * 3. Characters (as a last resort for very long words or strings without spaces)
 *
 * @param {string} text - The input text to split into chunks
 * @param {number} maxLength - The maximum character length for each chunk
 * @returns {string[]} An array of text chunks, each under the maxLength limit
 */
export const splitTextIntoTelegramChunks = (text, maxLength) => {
	// Initialize array to store chunks and current chunk being built
	const chunks = [];
	// Split text into lines for processing
	const lines = text.split('\n');
	// Track the current chunk being constructed
	let currentChunk = '';

	// Process each line individually
	for (const line of lines) {
		// Handle lines that are longer than the maximum length individually
		if (line.length > maxLength) {
			// If we have a partially built chunk, save it first
			if (currentChunk.length > 0) {
				chunks.push(currentChunk);
				currentChunk = '';
			}

			// Process the long line by breaking it into smaller chunks
			let remainingLongLine = line;
			while (remainingLongLine.length > maxLength) {
				// Start with a split at the maximum length
				let splitPoint = maxLength;
				// Look at the portion that would be in the current chunk
				const tempPart = remainingLongLine.substring(0, maxLength);
				// Find the last space in this portion to split at a word boundary
				const lastSpaceIndex = tempPart.lastIndexOf(' ');

				// Only split at the space if it's not too close to the beginning
				// This prevents creating very small chunks at the start
				if (lastSpaceIndex > maxLength * 0.8) {
					splitPoint = lastSpaceIndex;
				}

				// Extract the chunk up to the split point and add to results
				chunks.push(remainingLongLine.substring(0, splitPoint));
				// Update the remaining portion, trimming any leading whitespace
				remainingLongLine = remainingLongLine.substring(splitPoint).trimStart();
			}

			// The remaining portion becomes the start of the next chunk
			currentChunk = remainingLongLine;
		} else {
			// Line is within the length limit
			// Create a potential new chunk by combining current chunk with this line
			const potentialNewChunk = currentChunk.length === 0 ? line : `${currentChunk}\n${line}`;

			if (potentialNewChunk.length > maxLength) {
				// Adding this line would exceed the limit
				// Save the current chunk and start a new one with this line
				chunks.push(currentChunk);
				currentChunk = line;
			} else {
				// Adding this line fits within the limit
				// Append it to the current chunk
				currentChunk = potentialNewChunk;
			}
		}
	}

	// Add any remaining content in the current chunk to the results
	if (currentChunk.length > 0) {
		chunks.push(currentChunk);
	}

	return chunks;
};
