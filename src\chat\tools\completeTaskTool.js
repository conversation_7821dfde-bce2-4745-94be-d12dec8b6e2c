import { DynamicTool } from '@langchain/core/tools';

import { parseTaskDescription } from './parseInput.js';

/**
 * Create a LangChain DynamicTool to mark a task as completed by description.
 * Single responsibility: construct and return the complete_task tool.
 *
 * @param {TaskManager} taskManager
 * @param {string|number} userId
 * @returns {DynamicTool}
 */
export function completeTaskTool(taskManager, userId) {
	return new DynamicTool({
		name: 'complete_task',
		description:
			'Use this tool to mark a task as completed when the user indicates they have finished it. Provide a description or partial description of the task to complete.',
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot complete task.';
				}

				const taskDescription = parseTaskDescription(input);
				if (!taskDescription) {
					return 'Error: Task description is required to complete a task. Please specify which task you want to mark as completed.';
				}

				const completedTask = await taskManager.completeTaskByDescription(userId, taskDescription);

				if (completedTask) {
					console.log(`complete_task: Task completed for user ${userId}: ${completedTask.description}`);
					return `✅ Great job! I've marked "${completedTask.description}" as completed.`;
				} else {
					return `I couldn't find a pending task matching "${taskDescription}". Use the list_tasks tool to see your current tasks.`;
				}
			} catch (error) {
				console.error('Error in complete_task tool:', error);
				return 'Error: Failed to complete the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description: 'The description or partial description of the task to mark as completed.',
				},
			},
			required: ['description'],
		},
	});
}
