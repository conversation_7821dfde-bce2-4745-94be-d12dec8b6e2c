# Harmony Agent

A high-performance, AI-powered Telegram bot built using Vercel serverless functions. This bot, named Harmony, leverages multiple AI providers (primarily Google's Gemini AI) for context-aware, conversational responses with advanced orchestration capabilities.

## Features

- **AI-Powered Conversations**: Uses multiple AI providers (Gemini AI, fast models) with a custom personality and provider rotation
- **Context-Aware Responses**: Maintains conversation history, user facts, and semantic search for meaningful interactions
- **Media Processing**: Supports photos, documents, and media groups with OCR capabilities
- **Proactive Messaging**: Generates and sends "monologues" or thoughts to chats
- **User Access Control**: Configurable PRO user tiers with whitelisting capabilities
- **Smart Message Filtering**: Processes mentions, private messages, and media content
- **Typing Indicators**: Shows realistic typing status during processing
- **Multi-language Support**: Primarily Indonesian with organic Japanese phrases
- **Task Management**: Complete task creation, listing, completion, and reminder system
- **Web Search Integration**: Exa search integration for real-time web content retrieval
- **Intelligent Caching**: Upstash Redis for caching and conversation history
- **Vector Storage**: Upstash Vector for semantic search and message embedding
- **Advanced Monitoring**: Langsmith integration with custom LangfuseManager
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Fact Extraction**: Automatic extraction and storage of user facts for personalized interactions
- **Command Handling**: Modular command detection and processing system

## Project Structure

```
src/
├── index.js                 # Main application entry point
├── routes/                  # API route handlers
│   ├── telegram.routes.js   # Telegram webhook endpoints
│   ├── health.routes.js     # Health check endpoints
│   ├── thoughts.routes.js   # Monologue generation endpoints
│   └── simple.routes.js     # Utility endpoints
├── chat/                    # Core chat processing logic
│   ├── messageProcessor.js  # Main message orchestration
│   ├── commands/           # Command detection and routing
│   ├── gemini-ai/          # Modular AI provider architecture
│   ├── history/            # Conversation history management
│   ├── facts/              # User fact extraction and management
│   ├── tasks/              # Task management system
│   ├── search/             # Semantic search integration
│   └── response/           # Response handling utilities
├── redis/                  # Redis services and utilities
│   ├── redisClient.js      # Redis client configuration
│   ├── services/           # Redis service implementations
│   └── utils.js            # Redis utility functions
├── middleware/             # Application middleware
│   ├── errorHandler.js     # Centralized error handling
│   ├── userWhitelist.js    # User access control
│   └── validation.js       # Request validation
├── constants/              # Application constants
│   ├── ai.js              # AI-related constants
│   ├── config.js          # Configuration constants
│   ├── errors.js          # Error constants
│   └── prompts/           # AI prompt templates
├── utils/                  # Utility functions
│   ├── CircuitBreaker.js  # Circuit breaker pattern
│   ├── RedisBatchManager.js # Redis batch operations
│   └── vectorStorage.js   # Vector storage utilities
└── workers/               # Background workers
    └── reminder/          # Reminder system workers
```

## API Endpoints

### Core Endpoints

#### `GET /health`

Health check endpoint for monitoring.

**Response:**

```json
{
	"status": "healthy",
	"timestamp": "2024-01-01T00:00:00.000Z",
	"environment": "production"
}
```

#### `POST /hrmny`

Primary webhook endpoint for Telegram bot integration.

**Features:**

- AI-powered conversations with multiple provider support
- Context-aware responses with conversation history and user facts
- Media processing with OCR capabilities
- Smart message filtering and command detection
- Typing indicators during processing
- Task management system integration
- Web search capabilities via Exa

#### `POST /thoughts`

Endpoint for AI monologue generation and delivery to specified Telegram chats.

**Features:**

- Proactive messaging capabilities
- Rate limiting to prevent spam
- Intelligent topic generation
- Multi-provider AI support

### Telegram Integration

The bot supports comprehensive Telegram features including:

- Private and group message handling
- Media group processing
- Reply context understanding
- Command detection (`/commands`, `/tasks`, etc.)
- Callback query handling for interactive messages

## Deployment

This project is deployed on Vercel using serverless functions.

### Requirements

- Node.js 18+
- npm or yarn
- Vercel account
- Upstash Redis instance
- Upstash Vector database
- PostgreSQL database (for LangGraph checkpoints)
- Telegram bot token
- AI provider API keys (Gemini, etc.)

### Environment Variables

Configure these in your Vercel project settings:

```bash
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username
GEMINI_API_KEY=your_gemini_api_key_1,your_gemini_api_key_2
EXASEARCH_API_KEY=your_exa_search_api_key
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token
UPSTASH_VECTOR_REST_URL=your_upstash_vector_url
UPSTASH_VECTOR_REST_TOKEN=your_upstash_vector_token
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
PRO_USERS=*********,*********
DATABASE_URL=your_postgresql_connection_string
```

### Setup

1. **Clone the repository:**

    ```sh
    git clone <repository-url>
    cd harmony-agent
    ```

2. **Install dependencies:**

    ```sh
    npm install
    ```

3. **Configure environment variables:**
    - Set up all required environment variables in your Vercel project
    - Configure Telegram webhook to point to your deployed endpoint

4. **Deploy:**
    ```sh
    npm run dev  # For local development
    # Deploy to Vercel using Vercel CLI or GitHub integration
    ```

### Development

To start the development server:

```sh
npm run dev
```

For code formatting and linting:

```sh
npm run format          # Format code with Prettier
npm run auto-fix        # Run ESLint with automatic fixes
npm run format-fix      # Combine ESLint auto-fix and Prettier formatting
```

## Architecture

- **Framework**: Hono.js for lightweight, fast routing with secure headers
- **Runtime**: Vercel serverless functions (Node.js ES Modules)
- **AI Integration**: Multiple providers (Gemini AI, fast models) via LangChain with provider rotation
- **Database**:
    - Upstash Redis for caching and conversation history
    - Upstash Vector for semantic search and embeddings
    - PostgreSQL for LangGraph checkpoints
- **Search**: Exa search integration for web content retrieval
- **Monitoring**: Langsmith via `langfuse-langchain` with custom LangfuseManager
- **Error Handling**: Centralized error handling middleware with custom error types
- **Task Management**: Complete task system with reminders and callback support

## Advanced Features

### Provider Rotation

- Multiple AI providers with configurable priority
- API key management and automatic rotation
- Fallback mechanisms for improved reliability

### Task Management System

- Task creation, listing, completion, and deletion
- Reminder functionality with Telegram callback support
- Intelligent task parsing and formatting

### Semantic Search

- Message embedding generation and storage
- Context-aware search capabilities
- Vector-based similarity matching

### Fact Extraction

- Automatic extraction of user facts from conversations
- Persistent storage for personalized interactions
- Contextual fact retrieval

### Rate Limiting

- Intelligent rate limiting for monologue generation
- Configurable limits based on user tiers
- Prevention of spam and abuse

## Development Conventions

- **Code Style**: Enforced by Prettier and ESLint (config in `eslint.config.mjs`, `.prettierrc`)
- **Modules**: ES Modules (type: "module" in `package.json`)
- **Architecture**: Modular, event-driven processing for Telegram webhooks
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Configuration**: Environment variables for all sensitive configuration

## License

This project is private and proprietary.
