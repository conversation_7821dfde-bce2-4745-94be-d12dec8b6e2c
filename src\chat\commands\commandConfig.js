/**
 * Command configuration object that maps command types to their trigger strings.
 * This centralized configuration allows for flexible command handling with
 * multiple aliases for the same command type.
 *
 * Command types are defined as constants that map to arrays of trigger strings.
 * The command detector will match any of these trigger strings (case-insensitive)
 * and return the corresponding command type for routing to appropriate handlers.
 *
 * @module commands/commandConfig
 * @type {Object<string, string[]>}
 * @property {string[]} CLEAR_HISTORY - Clears chat history and associated data
 * @property {string[]} LIST_TASKS - Lists all active tasks for the user
 * @property {string[]} COMPLETE_TASK - Marks a task as completed
 * @property {string[]} DELETE_TASK - Deletes a specific task
 *
 * @example
 * // Adding a new command with multiple aliases:
 * const COMMANDS = {
 *   CLEAR_HISTORY: ['/clear', '/reset', '/start-over'],
 *   HELP: ['/help', '/info', '/assistance'],
 *   LIST_TASKS: ['/tasks', '/list', '/todo']
 * };
 *
 * @example
 * // Usage in command detection:
 * const commandType = detectCommand('/clear'); // Returns 'CLEAR_HISTORY'
 * const commandType2 = detectCommand('/tasks list'); // Returns 'LIST_TASKS'
 */
export const COMMANDS = {
	/**
	 * Clears the chat history and all associated conversation data from Redis.
	 * This provides users with a fresh start for their conversation.
	 * @type {string[]}
	 */
	CLEAR_HISTORY: ['/clear'],

	/**
	 * Lists all active tasks for the current user with their status and details.
	 * @type {string[]}
	 */
	LIST_TASKS: ['/tasks'],

	/**
	 * Marks a specific task as completed. Typically requires task ID as argument.
	 * @type {string[]}
	 */
	COMPLETE_TASK: ['/done'],

	/**
	 * Deletes a specific task permanently. Typically requires task ID as argument.
	 * @type {string[]}
	 */
	DELETE_TASK: ['/delete_task'],
};
