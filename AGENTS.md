# Project Overview: Harmony Agent

This is a high-performance, AI-powered Telegram bot named <PERSON>, built using Vercel serverless functions. It leverages multiple AI providers (primarily Google's Gemini AI) for context-aware, conversational responses with advanced orchestration capabilities. Key features include:

- AI-powered conversations with a custom personality and multiple provider support
- Context-aware responses using conversation history, user facts, and semantic search
- Media processing (photos, documents, media groups) with OCR capabilities
- Proactive messaging capabilities and monologue generation
- User whitelisting for access control with PRO user tiers
- Smart message filtering (mentions, private messages, media)
- Typing indicators during processing
- Multi-language support (primarily Indonesian with Japanese phrases)
- Intelligent caching and vector storage using Upstash services
- Web search integration via Exa search
- Task management system with reminder capabilities

## Development Environment

- **Runtime:** Node.js (module type: ES Module)
- **Framework:** Hono.js for routing with secure headers
- **Deployment:** Vercel serverless functions
- **AI:** Multiple providers (Gemini AI, fast models) via LangChain with provider rotation
- **Database:** Upstash Redis for caching/history, Upstash Vector for semantic search, PostgreSQL for LangGraph checkpoints
- **Search:** Exa search integration for web content
- **Monitoring:** <PERSON><PERSON> (via `langfuse-langchain`) with custom LangfuseManager
- **Key Dependencies:** `hono`, `@langchain/*`, `@upstash/*`, `nanoid`, `exa-js`, `pg`, `langfuse-langchain`, `@vercel/functions`

## Project Structure

The main application logic resides in `src/`. The entry point `src/index.js` sets up Hono middleware and routes. Routes are defined in `src/routes/` with the following endpoints:

- `/hrmny` (POST) - Telegram webhook for message processing
- `/health` (GET) - Health check endpoint
- `/thoughts` (POST) - AI monologue generation and delivery
- `/simple` - Utility endpoints (not detailed in current documentation)

Core processing logic for Telegram messages is in `src/chat/messageProcessor.js` which orchestrates:

- Command detection and routing (`src/chat/commands/`)
- Context preparation with chat history and user facts
- Attachment processing with OCR
- AI response generation with dynamic model selection
- Response handling and error management

The AI integration has been refactored into a modular architecture in `src/chat/gemini-ai/` featuring:

- Multiple provider support with configurable priority
- API key management and rotation
- Model factory pattern
- Embedding generation
- Comprehensive error handling

Redis interactions are managed through `src/redis.js` and its submodules for:

- Message logging and retrieval
- Fact extraction and storage
- Media group handling
- User facts management

## Building and Running

This project is configured for deployment and local development using Vercel.

**Key Scripts (from `package.json`):**

- `npm run dev`: Starts the Vercel development server
- `npm run format`: Formats code using Prettier
- `npm run auto-fix`: Runs ESLint with automatic fixes
- `npm run format-fix`: Combines ESLint auto-fix and Prettier formatting

## Environment Variables Required

The following environment variables must be configured:

- `TELEGRAM_BOT_TOKEN`: Telegram bot authentication token
- `TELEGRAM_BOT_USERNAME`: Bot username for command detection
- `GEMINI_API_KEY`: Google Gemini API key (can be multiple keys for rotation)
- `EXASEARCH_API_KEY`: Exa search API key for web search integration
- `UPSTASH_REDIS_REST_URL`: Upstash Redis connection URL
- `UPSTASH_REDIS_REST_TOKEN`: Upstash Redis authentication token
- `UPSTASH_VECTOR_REST_URL`: Upstash Vector database URL
- `UPSTASH_VECTOR_REST_TOKEN`: Upstash Vector authentication token
- `LANGFUSE_PUBLIC_KEY`: Langfuse public key for monitoring
- `LANGFUSE_SECRET_KEY`: Langfuse secret key for monitoring
- `PRO_USERS`: Comma-separated list of user IDs with PRO access
- `DATABASE_URL`: PostgreSQL connection URL for LangGraph checkpoints

## Development Conventions

- **Code Style:** Enforced by Prettier and ESLint (config in `eslint.config.mjs`, `.prettierrc`)
- **Modules:** Uses ES Modules (type: "module" in `package.json`)
- **Architecture:** Serverless functions (Vercel), event-driven processing for Telegram webhooks
- **Error Handling:** Centralized error handling middleware (`src/middleware/errorHandler.js`) with custom error types
- **AI Integration:** Advanced LangChain orchestration with multiple providers, embedding generation, and web search
- **Configuration:** Environment variables for all sensitive configuration with comprehensive validation
- **Testing:** No explicit testing commands or frameworks are configured in `package.json`. Testing practices are not immediately clear from the codebase.

## Advanced Features

- **Provider Rotation:** Multiple AI providers with configurable priority and fallback
- **Rate Limiting:** Intelligent rate limiting for AI monologue generation
- **Complexity Scoring:** Message complexity analysis for dynamic model selection (currently commented out)
- **Tool Calling:** AI function calling for task management and web search
- **Embedding Generation:** Text embedding capabilities for semantic search
- **Fact Extraction:** Automatic extraction and storage of user facts from conversations
- **Reminder System:** Task reminder functionality with Telegram callback support
