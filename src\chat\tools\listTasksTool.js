import { DynamicTool } from '@langchain/core/tools';

/**
 * Create a LangChain DynamicTool to list tasks for a user.
 * Single responsibility: construct and return the list_tasks tool.
 *
 * @param {TaskManager} taskManager
 * @param {string|number} userId
 * @returns {DynamicTool}
 */
export function listTasksTool(taskManager, userId) {
	return new DynamicTool({
		name: 'list_tasks',
		description:
			'Use this tool to retrieve and display all tasks for the user. This shows both pending and completed tasks with their current status.',
		func: async () => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot list tasks.';
				}

				const tasks = await taskManager.getTasks(userId);

				if (!Array.isArray(tasks) || tasks.length === 0) {
					return 'You have no tasks yet. Use the create_task tool to add some tasks to your list.';
				}

				const pendingTasks = tasks.filter((task) => task.status === 'pending');
				const completedTasks = tasks.filter((task) => task.status === 'completed');

				let response = `Found ${tasks.length} task(s):\n\n`;

				if (pendingTasks.length > 0) {
					response += `**Pending Tasks (${pendingTasks.length}):**\n`;
					pendingTasks.forEach((task, index) => {
						const createdDate = task.createdAt ? new Date(task.createdAt).toLocaleDateString() : 'Unknown';
						response += `${index + 1}. ${task.description} (Created: ${createdDate}, ID: ${task.id})\n`;
					});
					response += '\n';
				}

				if (completedTasks.length > 0) {
					response += `**Completed Tasks (${completedTasks.length}):**\n`;
					completedTasks.forEach((task, index) => {
						const createdDate = task.createdAt ? new Date(task.createdAt).toLocaleDateString() : 'Unknown';
						response += `${index + 1}. ✅ ${task.description} (Created: ${createdDate}, ID: ${task.id})\n`;
					});
				}

				console.log(`list_tasks: Listed ${tasks.length} tasks for user ${userId}`);
				return response.trim();
			} catch (error) {
				console.error('Error in list_tasks tool:', error);
				return 'Error: Failed to retrieve tasks due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {},
			additionalProperties: false,
		},
	});
}
