/**
 * @fileoverview Chat context management for AI interactions.
 *
 * This module handles the preparation of conversation context by retrieving
 * and filtering message history from Redis storage. It ensures clean,
 * deduplicated message sequences for AI processing while maintaining
 * efficient error handling and graceful degradation.
 *
 * Key responsibilities:
 * - Retrieve conversation history from Redis
 * - Remove duplicate messages based on timestamp
 * - Provide fallback context on errors
 *
 * @module chatContext
 */

// Core dependencies
import { getPreviousMessages } from '../redis.js';
import { prepareFactContext } from './facts/factManager.js';
import { handleAIResponse } from './response/aiResponseHandler.js';

/**
 * Retrieves and prepares chat context from Redis storage.
 *
 * This function fetches the conversation history for a specific chat and removes
 * any duplicate messages that may have been stored with the same timestamp.
 * It provides a clean message history for AI processing.
 *
 * Process:
 * 1. Fetch previous messages from Redis using chat ID and bot username
 * 2. Remove the last message if it matches the current message timestamp (prevents duplication)
 * 3. Return the cleaned message history
 *
 * @param {Object} env - Environment variables containing Redis configuration and secrets
 * @param {string|number} chatId - The Telegram chat ID to retrieve context for
 * @param {string} botUsername - The bot username to filter messages appropriately
 * @param {number} messageDate - Unix timestamp of the current message for deduplication
 * @returns {Promise<Object>} Object containing the prepared context:
 * @returns {Array} return.previousMessages - Array of previous message objects from Redis
 *
 * @example
 * const context = await prepareChatContext(env, "123456789", "myBot", Date.now());
 * console.log(context.previousMessages); // Array of message objects
 */
export async function prepareChatContext(env, chatId, botUsername, messageDate) {
	try {
		// Fetch conversation history from Redis storage
		let previousMessages = await getPreviousMessages(env, chatId, botUsername);

		// Remove duplicate message if it exists (prevents duplication when message was stored before processing)
		if (messageDate && previousMessages.length > 0 && previousMessages[previousMessages.length - 1].date === messageDate) {
			previousMessages.pop();
		}

		// Return cleaned message history for AI processing
		return {
			previousMessages,
		};
	} catch (error) {
		// Log error while maintaining functionality
		console.error('Error in prepareChatContext:', error);

		// Return empty history as fallback to ensure graceful degradation
		return {
			previousMessages: [],
		};
	}
}

/**
 * Re-exported functions for backward compatibility.
 *
 * These functions are maintained for existing code that imports them from this module.
 * New implementations should import these functions directly from their source modules:
 * - handleAIResponse: from './response/aiResponseHandler.js'
 * - prepareFactContext: from './facts/factManager.js'
 *
 * @deprecated Use direct imports from source modules for new code
 */
export { handleAIResponse, prepareFactContext };
