import { MessageLogger } from './services/MessageLogger.js';
import { MessageRetriever } from './services/MessageRetriever.js';

/**
 * Logs a Telegram message using the MessageLogger service.
 * @param {object} env - Environment variables
 * @param {string} chatId - The ID of the chat where the message was sent
 * @param {object} messageData - The message data to log
 * @returns {Promise} Promise that resolves when the message is logged
 */
export const logTelegramMessage = async (env, chatId, messageData) => {
	const logger = new MessageLogger(env);
	return logger.logMessage(chatId, messageData);
};

/**
 * Retrieves previous messages from a chat using the MessageRetriever service.
 * @param {object} env - Environment variables
 * @param {string} chatId - The ID of the chat to retrieve messages from
 * @param {string} botUsername - The username of the bot
 * @returns {Promise<Array>} Promise that resolves to an array of previous messages
 */
export const getPreviousMessages = async (env, chatId, botUsername) => {
	const retriever = new MessageRetriever(env);
	return retriever.getMessages(chatId, botUsername);
};
