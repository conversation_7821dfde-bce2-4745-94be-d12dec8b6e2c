import { waitUntil } from '@vercel/functions';
import { Hono } from 'hono';

import { detectCommand } from '../chat/commands/commandDetector.js';
import { processTelegramMessageInBackground } from '../chat/messageProcessor.js';
import { logTelegramMessage } from '../redis.js';
import { extractMessageDetails, handleInitialError, isValidWebhookData, parseWebhookData } from '../utils/telegramWebhookHelpers.js';

const telegramRoutes = new Hono();

/**
 * Telegram Webhook Endpoint for HRMNY
 * Receives messages from Telegram and processes them
 */
telegramRoutes.post('/hrmny', async (c) => {
	try {
		// Parse incoming webhook data (reuse if middleware already parsed)
		let webhookData = c.get('webhookData');
		try {
			if (!webhookData) {
				webhookData = await parseWebhookData(c.req);
				c.set('webhookData', webhookData);
			}
			// Avoid heavy stringify; log minimal info
			const hasMsg = !!(webhookData.message || webhookData.callback_query);
			const chatId = webhookData.message?.chat?.id || webhookData.callback_query?.message?.chat?.id;
			console.log('[Telegram Webhook] Received', { hasMsg, chatId });
		} catch (parseError) {
			console.error('Failed to parse webhook data:', parseError);
			return c.json({ success: false, error: 'Invalid request data' }, 400);
		}

		// Validate webhook data structure
		if (!isValidWebhookData(webhookData)) {
			console.warn('[Telegram Webhook] Invalid data: missing message or callback_query');
			return c.json({ success: false, error: 'Invalid data structure' }, 400);
		}

		// Extract message details from webhook data
		let messageDetails;
		try {
			messageDetails = extractMessageDetails(c.env, webhookData);
		} catch (extractionError) {
			console.error('Failed to extract message details:', extractionError);
			return c.json({ success: false, error: 'Failed to process message' }, 400);
		}

		const { chatId, text, photo = [], document = {}, messageDataForLogging } = messageDetails;

		// Early return if there's nothing to process
		if (!chatId || !messageDataForLogging || (!text && photo.length === 0 && !document?.file_id)) {
			console.info('[Telegram Webhook] No content to process for chat:', chatId);
			return c.text('ok');
		}

		/**
		 * Store message in Redis and vector database if it's not a command
		 * @param {string} text - The message text to store
		 */
		const StoreMessage = async (text) => {
			if (!detectCommand(text)) {
				// Log the message to Redis for later processing
				await logTelegramMessage(c.env, chatId, messageDataForLogging).catch((redisError) => {
					console.error('[Telegram Webhook] Redis logging error:', redisError);
				});
			}
		};

		// Build tasks array, avoid scheduling store if it's a command
		const tasks = [];
		if (!detectCommand(text)) {
			tasks.push(StoreMessage(text));
		}
		tasks.push(
			// Process the message in the background
			processTelegramMessageInBackground(c.env, webhookData).catch((err) => {
				console.error('[Telegram Webhook] Background processing error:', err);
			}),
		);

		// Schedule all async operations to run in parallel
		waitUntil(Promise.all(tasks));

		console.log('[Telegram Webhook] Scheduled logging and background processing for chat:', chatId);

		// Immediately acknowledge receipt to Telegram with a tiny payload
		return c.text('ok');
	} catch (error) {
		await handleInitialError(c, error);
		return c.json({ success: false, error: 'Internal server error' }, 500);
	}
});

export default telegramRoutes;
