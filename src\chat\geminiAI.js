/**
 * @fileoverview Main entry point for the refactored GeminiAI module.
 *
 * This module serves as the backward-compatible entry point for the Gemini AI integration
 * after a comprehensive refactoring into a modular architecture. All functionality is
 * re-exported from the new modular structure to ensure existing consumers continue to work
 * without modification.
 *
 * The refactored architecture provides:
 * - Better separation of concerns through focused modules
 * - Improved testability with isolated components
 * - Enhanced maintainability with clear module boundaries
 * - Support for dependency injection and configuration
 * - Comprehensive error handling with specific error types
 *
 * @module chat/geminiAI
 * @see {@link module:chat/gemini-ai/index} for the new modular API
 * @deprecated For new code, import directly from the modular structure in './gemini-ai/'
 *
 * @example
 * // Legacy usage (still supported):
 * import { callGenerativeAI } from './chat/geminiAI.js';
 *
 * @example
 * // Recommended usage for new code:
 * import { callGenerativeAI } from './chat/gemini-ai/index.js';
 */

// Re-export all functionality from the new modular structure
export * from './gemini-ai/index.js';
