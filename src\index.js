import { Hono } from 'hono';
import { secureHeaders } from 'hono/secure-headers';

// Import custom middleware for request processing.
import { errorHandlerMiddleware, userWhitelistMiddleware, validationMiddleware } from './middleware/index.js';
// Import application-specific routes.
import routes from './routes/index.js';

// Initialize the Hono application instance.
const app = new Hono();

/**
 * ==============================================================================
 * Global Middleware Registration
 * ==============================================================================
 * Middleware is registered globally and executes on every incoming request.
 * The order of registration is crucial as it defines the execution pipeline.
 */

// The error handler is registered first to catch any errors from subsequent middleware and routes.
app.use('*', errorHandlerMiddleware);

// Apply secure HTTP headers for enhanced security.
app.use('*', secureHeaders());

// Custom middleware for validating incoming request data.
app.use('*', validationMiddleware);

// User whitelist middleware for the harmony bot.
app.use('*', userWhitelistMiddleware);

/**
 * ==============================================================================
 * Route Registration
 * ==============================================================================
 * Mounts the application routes, making them accessible under the root path.
 */
app.route('/', routes);

/**
 * ==============================================================================
 * Vercel Serverless Function Export
 * ==============================================================================
 * Exports a default handler function compatible with Vercel's serverless runtime.
 * This adapts the Hono app for Vercel's request/response format.
 */
export default async function handler(request, response) {
	try {
		// Optimized URL handling with proper validation
		const url = createRequestUrl(request.url);

		// Streamlined body processing
		const body = processRequestBody(request.body);

		// Efficient header filtering using Set for O(1) lookups
		const headers = createFilteredHeaders(request.headers);

		// Create the Fetch API Request object
		const fetchRequest = new Request(url, {
			method: request.method,
			headers,
			body,
		});

		// Use the Hono app's fetch method with environment context
		const fetchResponse = await app.fetch(fetchRequest, process.env);

		// Set status and headers
		response.status(fetchResponse.status);
		setResponseHeaders(response, fetchResponse.headers);

		// Stream response body for better memory efficiency
		await streamResponseBody(response, fetchResponse);
	} catch (error) {
		handleHandlerError(error, response);
	}
}

/**
 * Creates a valid URL from the request URL, handling both absolute and relative URLs
 */
function createRequestUrl(requestUrl) {
	try {
		return new URL(requestUrl);
	} catch {
		// For relative URLs, use a base URL that won't cause issues
		return new URL(requestUrl, 'http://localhost');
	}
}

/**
 * Processes the request body efficiently
 */
function processRequestBody(body) {
	if (!body) return null;

	if (typeof body === 'string' || Buffer.isBuffer(body)) {
		return body;
	}

	if (typeof body === 'object') {
		return JSON.stringify(body);
	}

	// For other types, convert to string
	return String(body);
}

/**
 * Creates filtered headers using Set for efficient lookups
 */
function createFilteredHeaders(requestHeaders) {
	const headers = new Headers();
	const headersToSkip = new Set(['content-length', 'host']);

	for (const [key, value] of Object.entries(requestHeaders)) {
		const lowerKey = key.toLowerCase();
		if (!headersToSkip.has(lowerKey) && value !== undefined && value !== null) {
			headers.append(key, value);
		}
	}

	return headers;
}

/**
 * Sets response headers efficiently
 */
function setResponseHeaders(response, headers) {
	for (const [key, value] of headers.entries()) {
		response.setHeader(key, value);
	}
}

/**
 * Streams response body for better memory efficiency
 */
async function streamResponseBody(response, fetchResponse) {
	if (!fetchResponse.body) {
		response.end();
		return;
	}

	// Use streaming instead of buffering the entire response
	const reader = fetchResponse.body.getReader();

	try {
		while (true) {
			const { done, value } = await reader.read();
			if (done) break;

			if (value) {
				response.write(Buffer.from(value));
			}
		}
		response.end();
	} finally {
		reader.releaseLock();
	}
}

/**
 * Handles errors with proper categorization and logging
 */
function handleHandlerError(error, response) {
	console.error('[VERCEL HANDLER] Error processing request:', {
		message: error.message,
		stack: error.stack,
		timestamp: new Date().toISOString(),
	});

	// Return appropriate error response
	response.status(500).json({
		error: 'Internal Server Error',
		message: 'An unexpected error occurred while processing your request.',
		timestamp: new Date().toISOString(),
	});
}
