import { detectCommand } from './commandDetector.js';
import { processCommand } from './commandRouter.js';

/**
 * Main entry point for command handling in the Telegram bot system.
 *
 * This function orchestrates the complete command processing pipeline:
 * 1. Detects if the input text contains a valid command using command detection
 * 2. If a command is detected, routes it to the appropriate handler for processing
 * 3. Returns a boolean indicating whether a command was successfully processed
 *
 * The function handles both exact command matches and commands with arguments,
 * ensuring proper routing to command-specific handlers while maintaining
 * the original message context for argument parsing.
 *
 * @module commands/index
 * @param {string} text - The message text to check for commands (may include arguments)
 * @param {Object} env - Environment variables containing bot configuration and API keys
 * @param {Object} messageData - Message metadata containing chat ID, user ID, and message details
 * @param {string} botUsername - The bot's username used for context filtering and validation
 * @returns {Promise<boolean>} True if a command was detected and successfully processed, false otherwise
 *
 * @example
 * // Basic command detection and processing
 * const handled = await handleCommand('/clear', env, messageData, 'mybot');
 * console.log(handled); // true if command processed
 *
 * @example
 * // Command with arguments
 * const handled = await handleCommand('/done task123', env, messageData, 'mybot');
 * console.log(handled); // true if COMPLETE_TASK command processed
 *
 * @example
 * // Non-command message
 * const handled = await handleCommand('Hello there!', env, messageData, 'mybot');
 * console.log(handled); // false - no command detected
 *
 * @throws {Error} If command processing encounters an unexpected error
 * (individual command handlers may throw specific errors)
 */
export async function handleCommand(text, env, messageData, botUsername) {
	// Detect if the text contains any registered command
	const commandType = detectCommand(text);

	// Return early if no command is detected to avoid unnecessary processing
	if (!commandType) {
		return false;
	}

	// Log command detection for monitoring and debugging
	console.log(`Command detected: ${commandType} from user ${messageData.userId} in chat ${messageData.chatId}`);

	// Route the command to the appropriate handler with full context including arguments
	// The full text is passed to allow handlers to parse arguments from the command
	return await processCommand(commandType, env, messageData, botUsername, text);
}
