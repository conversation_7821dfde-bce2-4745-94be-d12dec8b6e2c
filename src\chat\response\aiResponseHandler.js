/**
 * Handles AI response processing and delivery
 * @module aiResponseHandler
 */
import {
	REFINE_RESPONSE,
	REFINE_RESPONSE_SYSTEM_PROMPT,
	REFINE_RESPONSE_TEMPERATURE,
	SUMMARY_PROMPT,
	SUMMARY_SYSTEM_PROMPT,
	SUMMARY_TEMPERATURE,
} from '../../constants/index.js';
import { logTelegramMessage } from '../../redis.js';
import { callFastGenerativeAI } from '../geminiAI.js';
import { formatTimestamp } from '../utils/formatUtils.js';
import { generateSuggestions } from './suggestionGenerator.js';

/**
 * <PERSON>les logging, escaping, and sending the AI response back to Telegram
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string|Array} aiResponseText - The AI response text (can be an array of responses)
 * @param {string|number} originalMessageId - The ID of the original message being responded to
 * @param {string} originalMessageText - The original user message text.
 * @param {string} userFacts - The user's extracted facts.
 * @param {string} conversationHistory - The recent conversation history.
 * @param {Array} [aiResponseThoughts] - Optional array of AI thoughts
 * @returns {Promise<void>}
 */
export async function handleAIResponse(
	env,
	chatId,
	responseText,
	originalMessageId,
	originalMessageText,
	userFacts,
	conversationHistory = '',
) {
	// Import here to avoid circular dependencies
	const { escapeHtml } = await import('../telegramUtils.js');
	const { sendLongTelegramMessage } = await import('../telegram/index.js');

	try {
		// Refine the AI response if enabled
		if (REFINE_RESPONSE) {
			const refineConfig = {
				temperature: REFINE_RESPONSE_TEMPERATURE,
				systemInstruction: REFINE_RESPONSE_SYSTEM_PROMPT,
				inferenceProvider: 'groq',
				model: 'moonshotai/kimi-k2-instruct',
				traceTags: ['response-refine'],
			};

			const refineContents = [
				{
					role: 'user',
					parts: [{ text: responseText }],
				},
			];

			const refinedResponse = await callFastGenerativeAI(env, refineConfig, refineContents);
			responseText = refinedResponse.text;
		}

		let suggestions = [];
		if (env.ENABLE_SUGGESTIONS === 'true') {
			// Generate suggestions first, as they are part of the message sent to the user.
			suggestions = await generateSuggestions(
				env,
				originalMessageText,
				responseText,
				userFacts,
				conversationHistory,
				formatTimestamp(Date.now() / 1000, env, 'date'),
				formatTimestamp(Date.now() / 1000, env, 'time'),
			);
		}

		// Send the final response to Telegram as soon as it's ready
		await _sendResponse(env, chatId, responseText, originalMessageId, sendLongTelegramMessage, escapeHtml, suggestions);
	} catch (error) {
		console.error('Error in handleAIResponse:', error);
		throw error;
	} finally {
		// Log the AI response to Redis and store its embedding
		await _logAndStoreResponse(env, chatId, responseText, originalMessageId, []);
	}
}

/**
 * Sends the AI response to the user
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to send
 * @param {string|number} originalMessageId - The original message ID
 * @param {Function} sendMessage - Function to send the message
 * @param {Function} escapeFn - Function to escape and format text (HTML or Markdown)
 * @returns {Promise<void>}
 */
async function _sendResponse(env, chatId, responseText, originalMessageId, sendMessage, escapeFn, suggestions = []) {
	if (!responseText) {
		console.warn('Empty response text, nothing to send');
		return;
	}

	const formattedResponse = escapeFn(responseText);

	try {
		const messageOptions = { message_id: originalMessageId, parseMode: 'HTML' };
		const sendResult = await sendMessage(env, chatId, formattedResponse, messageOptions, suggestions);

		if (sendResult) {
			console.log(`Successfully sent AI response to chat ${chatId}`);
		} else {
			console.error(`Failed to send AI response to chat ${chatId}`);
		}
	} catch (error) {
		console.error(`Error sending AI response to chat ${chatId}:`, error);
		throw error;
	}
}

/**
 * Extracts response text from possible response formats
 * @private
 * @param {string|Array|Object} response - The AI response
 * @returns {string} Extracted text
 */
export function extractResponseText(response) {
	if (!response) return '';

	if (Array.isArray(response)) {
		const last = response[response.length - 1];
		return last?.text.trim() || '';
	}

	return typeof response === 'string' ? response.trim() : '';
}

/**
 * Logs the AI response to Redis and stores its embedding
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to log and store
 * @param {string|number} originalMessageId - The original message ID
 * @param {Array} thoughts - AI thoughts
 * @returns {Promise<void>}
 */
async function _logAndStoreResponse(env, chatId, responseText, originalMessageId, thoughts = []) {
	const aiMessageTimestamp = Date.now() / 1000;

	// Generate summary first so we can store it in both Redis and vector DB
	const summary = responseText ? await _generateResponseSummary(env, responseText) : '';

	// Log to Redis with summary
	logTelegramMessage(env, chatId, {
		text: responseText,
		summary: summary,
		thoughts,
		from: {
			username: env.TELEGRAM_BOT_NAME,
			is_bot: true,
		},
		date: aiMessageTimestamp,
	});

	console.log(`AI response for message ${originalMessageId} stored in Redis`);
}

/**
 * Generates a concise summary of the AI response using fast AI
 * @private
 * @param {Object} env - Environment variables
 * @param {string} responseText - The response text to summarize
 * @returns {Promise<string>} Generated summary or empty string if failed
 */
async function _generateResponseSummary(env, responseText) {
	try {
		const config = {
			temperature: SUMMARY_TEMPERATURE,
			systemInstruction: SUMMARY_SYSTEM_PROMPT,
			inferenceProvider: 'cerebras',
			model: 'gpt-oss-120b',
			traceTags: ['response-summary'],
		};

		const contents = [
			{
				role: 'user',
				parts: [
					{
						text: SUMMARY_PROMPT.replace('{AI_RESPONSE}', responseText),
					},
				],
			},
		];

		const response = await callFastGenerativeAI(env, config, contents);

		if (response && response.text) {
			// Clean and truncate the summary
			const summary = response.text.trim().replace(/\n/g, ' ');
			return summary;
		}

		// Fallback to simple truncation if AI summary fails
		return responseText.substring(0, 100) + (responseText.length > 100 ? '...' : '');
	} catch (error) {
		console.warn('Failed to generate AI response summary:', error.message);
		// Fallback to simple truncation
		return responseText.substring(0, 100) + (responseText.length > 100 ? '...' : '');
	}
}
