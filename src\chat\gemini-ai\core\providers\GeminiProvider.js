/**
 * @fileoverview
 * Manages interactions with the Gemini API for text completions. This provider handles API key
 * rotation and fallback mechanisms across different Gemini models to ensure reliability and
 * maximize success rates for completion requests.
 */

import { CIRCUIT_BREAKER_CONFIGS, getCircuitBreaker } from '../../../../utils/CircuitBreaker.js';
import { DEFAULT_GEMINI_MODELS } from '../../config/constants.js';
import { ApiKeyError } from '../../errors/GeminiErrors.js';
import { ModelFactory } from '../../factories/ModelFactory.js';
import { langfuseManager } from '../../utils/LangfuseManager.js';
import { MessageConverter } from '../../utils/MessageConverter.js';

/**
 * Implements a provider for Google's Gemini models, with support for API key management
 * and model fallbacks.
 */
export class GeminiProvider {
	/**
	 * Initializes a new instance of the GeminiProvider.
	 * @param {object} apiKeyManager - An instance of SimpleApiKeyManager to handle Gemini API keys.
	 */
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
	}

	/**
	 * Attempts to generate a text completion by trying different models and API keys.
	 * It iterates through available API keys and specified models, making completion
	 * requests until one succeeds.
	 * @param {object} env - The environment variables, used to load API keys.
	 * @param {object} config - Configuration for the completion request, including an optional model.
	 * @param {Array<object>} contents - The content or message history for the completion.
	 * @param {ErrorAggregator} errorAggregator - An object to collect errors from failed attempts.
	 * @returns {Promise<object|null>} The completion result or null if all attempts fail.
	 * @throws {ApiKeyError} If no Gemini API keys are available.
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		const langfuseHandler = langfuseManager.getHandler(env, config);

		// Add the database connection string to the config
		const configWithConnectionString = {
			...config,
			connectionString: env.SUPABASE_URL,
		};

		// Get the list of models for the provider, handling potential whitespace and empty entries
		let modelsToTry = DEFAULT_GEMINI_MODELS.split(',')
			.map((m) => m.trim())
			.filter(Boolean);

		// If a specific model is requested and available for this provider, try it first.
		if (config.model && modelsToTry.includes(config.model)) {
			modelsToTry = [config.model, ...modelsToTry.filter((m) => m !== config.model)];
		}

		// Try with available API keys
		const apiKey = this.apiKeyManager.getNextGeminiApiKey(env);
		if (!apiKey) {
			throw new ApiKeyError('No Gemini API key available.');
		}

		for (const modelName of modelsToTry) {
			const circuitBreaker = getCircuitBreaker(`gemini_api_${modelName}`, CIRCUIT_BREAKER_CONFIGS.GEMINI_API);
			if (circuitBreaker.isOpen()) {
				console.warn(`[GeminiProvider] Circuit breaker is open for ${modelName}, skipping`);
				errorAggregator.addError(`gemini/${modelName}`, new Error('Circuit breaker is open'));
				continue;
			}
			try {
				const result = await circuitBreaker.execute(async () => {
					return await this._attemptModelCompletion(modelName, configWithConnectionString, apiKey, contents, langfuseHandler);
				});
				if (result) {
					return result;
				}
			} catch (error) {
				const context = `gemini/${modelName}`;
				console.error(`Failed chat completion with ${context}:`, error);
				errorAggregator.addError(context, error);
				this.apiKeyManager.updateKeyStatus(apiKey, error);
			}
		}
		return null;
	}

	/**
	 * Attempts a single chat completion with a specific model and API key.
	 * @param {string} modelName - The name of the Gemini model to use.
	 * @param {object} config - Configuration for the model.
	 * @param {string} apiKey - The API key for the request.
	 * @param {Array<object>} contents - The message content for the completion.
	 * @param {object} langfuseHandler - The handler for logging to Langfuse.
	 * @returns {Promise<object>} The result from the model.
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler) {
		console.log(`Attempting generation with model: ${modelName}`);

		// Get the database connection string from the config (assuming it's passed through)
		const connectionString = config.connectionString;
		const agent = await ModelFactory.createGeminiAgent(apiKey, modelName, config, connectionString);
		const messages = MessageConverter.convertToLangChainMessages(contents);

		const invokeOptions = {};
		if (langfuseHandler) {
			invokeOptions.callbacks = [langfuseHandler];
		}

		if (config.isChat) {
			invokeOptions.configurable = {
				thread_id: config.threadId,
			};
		}

		const result = await agent.invoke({ messages }, invokeOptions);

		// Extract the content from the last message in the response.
		const lastMessage = result.messages[result.messages.length - 1];
		const responseContent = lastMessage.content;

		console.log(`✅ Successfully completed ${config.traceTags.join(', ')} with model: ${modelName}`);

		return {
			text: responseContent,
			thoughts: '', // Placeholder for any thoughts returned by the model
		};
	}
}
