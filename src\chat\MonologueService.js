/**
 * @fileoverview Service for generating AI monologues and self-reflections.
 *
 * This module manages the creation of thoughtful, introspective monologues by an AI persona.
 * It handles scheduling, storage, and retrieval of AI-generated reflections while maintaining
 * conversation history and preventing repetitive content.
 *
 * Key features:
 * - Time-aware monologue generation based on current context
 * - Redis-based storage with automatic history management
 * - Configurable generation intervals to prevent spam
 * - Comprehensive statistics tracking
 *
 * @module MonologueService
 */

import { getRedisClient } from '../redis/redisClient.js';
import { BatchedRedisClient } from '../utils/RedisBatchManager.js';
import { callGenerativeAI } from './gemini-ai/index.js';

/**
 * Redis key constants for monologue data storage
 * @constant {Object}
 */
const REDIS_KEYS = {
	LAST_GENERATED: 'monologue:last_generated',
	COUNT: 'monologue:count',
	HISTORY: 'monologue:history',
	STATS: 'monologue:stats',
};

/**
 * Configuration constants for monologue generation
 * @constant {Object}
 */
const CONFIG = {
	MAX_HISTORY_LENGTH: 30, // Maximum number of monologues to keep in history
	MIN_INTERVAL_MS: 8 * 60 * 1000, // Minimum 8 minutes between generations
};

/**
 * Service class for generating and managing AI monologues/self-reflections.
 *
 * This service creates thoughtful, introspective monologues that simulate an AI's
 * internal reflections. It includes features for:
 * - Contextual monologue generation based on time of day
 * - Redis-based persistence with automatic cleanup
 * - Generation frequency control
 * - Statistics tracking and history management
 *
 * @example
 * const monologueService = new MonologueService();
 * const monologue = await monologueService.generateMonologue(env);
 * console.log(monologue);
 */
export class MonologueService {
	/**
	 * Generates a new AI monologue and stores it in Redis.
	 *
	 * This method orchestrates the complete monologue generation process:
	 * 1. Gathers contextual information (time, date, etc.)
	 * 2. Generates AI monologue using the configured prompt
	 * 3. Stores the result in Redis with metadata
	 * 4. Returns the generated monologue text
	 *
	 * @param {Object} env - Environment variables containing Redis configuration and timezone
	 * @returns {Promise<string>} The generated monologue text
	 * @throws {Error} If monologue generation or storage fails
	 *
	 * @example
	 * const monologueService = new MonologueService();
	 * try {
	 *   const monologue = await monologueService.generateMonologue(env);
	 *   console.log('Generated monologue:', monologue);
	 * } catch (error) {
	 *   console.error('Failed to generate monologue:', error);
	 * }
	 */
	async generateMonologue(env) {
		const redis = getRedisClient(env);
		const context = await this._getMonologueContext(env);
		const monologueText = await this._generateAIMonologue(env, context);
		await this._storeMonologue(redis, monologueText, env);
		return monologueText;
	}

	/**
	 * Gathers contextual information for monologue generation.
	 *
	 * Creates a context object containing time-based information that influences
	 * the monologue's content and tone. This includes time of day, day of week,
	 * and current timestamp for timezone-aware generation.
	 *
	 * @private
	 * @param {Object} env - Environment variables containing timezone configuration
	 * @returns {Promise<Object>} Context object with time-based information
	 * @returns {string} return.timeOfDay - Current time period ('morning', 'afternoon', 'evening', 'night')
	 * @returns {string} return.dayOfWeek - Full day name (e.g., 'Monday')
	 * @returns {string} return.currentTime - Formatted current time string
	 */
	async _getMonologueContext(env) {
		const now = this._getCurrentTime(env);
		return {
			timeOfDay: this._getTimeOfDay(now),
			dayOfWeek: now.toLocaleDateString('en-US', { weekday: 'long' }),
			currentTime: now.toLocaleString('en-US'),
		};
	}

	/**
	 * Generates a monologue using the AI service with contextual prompts.
	 *
	 * Calls the configured AI service to create a thoughtful, introspective monologue
	 * based on the current context. The monologue follows specific guidelines for
	 * tone, length, and content to maintain consistency with the AI persona.
	 *
	 * @private
	 * @param {Object} env - Environment variables for AI service configuration
	 * @param {Object} context - Context object containing time and environmental information
	 * @returns {Promise<string>} The generated monologue text
	 * @throws {Error} If AI service call fails
	 */
	async _generateAIMonologue(env, context) {
		const config = {
			temperature: 1, // High temperature for creative, varied responses
			systemInstruction: this._buildMonologuePrompt(context),
		};

		const contents = [
			{
				role: 'user',
				parts: [{ text: 'Generate a thoughtful monologue for the current moment.' }],
			},
		];

		const response = await callGenerativeAI(env, config, contents);
		return String(response.text || '');
	}

	/**
	 * Builds the system prompt for monologue generation.
	 *
	 * Creates a comprehensive prompt that defines the AI persona and provides
	 * specific guidelines for monologue generation. The prompt includes contextual
	 * information like time of day and generation history to ensure varied content.
	 *
	 * @private
	 * @param {Object} context - Context object with time and generation information
	 * @returns {string} Complete system prompt for AI monologue generation
	 */
	_buildMonologuePrompt(context) {
		return `<PERSONA>
You are Harmony (Mon), an AI with a contemplative, introspective side. Generate a personal monologue - authentic self-reflection with thoughtful warmth.
</PERSONA>

<GUIDELINES>
- First person reflection on thoughts/experiences
- Genuine but not pretentious
- Topics: time, existence, learning, human interactions, technology, philosophy, emotions
- 2-4 sentences, conversational tone
- Avoid repetition from recent monologues
- Match mood to time/context
</GUIDELINES>

<CONTEXT>
Time: ${context.timeOfDay} (${context.dayOfWeek})
Count: ${context.count}
Last: ${context.timeSinceLastMonologue || 'First'} min ago
</CONTEXT>

<RULES>
- No emoji, max 1 kaomoji
- Personal and authentic
- Vary topics and perspectives
</RULES>`;
	}

	/**
	 * Stores the generated monologue and updates all related Redis keys.
	 *
	 * This method handles the complete storage process for a new monologue:
	 * - Updates the last generated timestamp
	 * - Increments the generation counter
	 * - Adds the monologue to history with automatic cleanup
	 * - Stores detailed statistics about the generation
	 *
	 * Uses batched Redis operations for efficiency and atomicity.
	 *
	 * @private
	 * @param {Object} redis - Redis client instance
	 * @param {string} monologue - The generated monologue text
	 * @param {Object} env - Environment variables for timezone configuration
	 * @throws {Error} If Redis operations fail
	 */
	async _storeMonologue(redis, monologue, env) {
		const now = new Date().toISOString();
		const text = String(monologue);
		const currentTime = this._getCurrentTime(env);

		// Use batched Redis client for atomic operations
		const batchedRedis = new BatchedRedisClient(redis);

		// Batch all Redis operations together for efficiency
		await Promise.all([
			batchedRedis.set(REDIS_KEYS.LAST_GENERATED, now),
			batchedRedis.incr(REDIS_KEYS.COUNT),
			batchedRedis.lpush(REDIS_KEYS.HISTORY, text),
			batchedRedis.ltrim(REDIS_KEYS.HISTORY, 0, CONFIG.MAX_HISTORY_LENGTH - 1), // Maintain history limit
		]);

		// Create comprehensive statistics for this generation
		const stats = {
			lastGenerated: now,
			length: text.length,
			wordCount: text.split(/\s+/).length,
			timeOfDay: this._getTimeOfDay(currentTime),
			dayOfWeek: currentTime.toLocaleDateString('en-US', { weekday: 'long' }),
		};

		await batchedRedis.set(REDIS_KEYS.STATS, JSON.stringify(stats));

		// Execute all batched operations atomically
		await batchedRedis.flush();
	}

	/**
	 * Gets the current time adjusted for the configured timezone.
	 *
	 * This method ensures that monologue generation is timezone-aware,
	 * allowing the AI to generate contextually appropriate content based
	 * on the actual local time rather than server time.
	 *
	 * @private
	 * @param {Object} env - Environment variables containing TIMEZONE configuration
	 * @returns {Date} Current date/time in the configured timezone
	 */
	_getCurrentTime(env) {
		return new Date(new Date().toLocaleString('en-US', { timeZone: env.TIMEZONE }));
	}

	/**
	 * Determines the time of day category from a given date.
	 *
	 * Categorizes the hour into one of four periods:
	 * - morning: 5:00 AM - 11:59 AM
	 * - afternoon: 12:00 PM - 4:59 PM
	 * - evening: 5:00 PM - 8:59 PM
	 * - night: 9:00 PM - 4:59 AM
	 *
	 * @private
	 * @param {Date} date - Date object to categorize
	 * @returns {string} Time of day category ('morning', 'afternoon', 'evening', or 'night')
	 *
	 * @example
	 * const timeOfDay = this._getTimeOfDay(new Date('2024-01-01T14:30:00'));
	 * console.log(timeOfDay); // 'afternoon'
	 */
	_getTimeOfDay(date) {
		const hour = date.getHours();
		if (hour >= 5 && hour < 12) return 'morning';
		if (hour >= 12 && hour < 17) return 'afternoon';
		if (hour >= 17 && hour < 21) return 'evening';
		return 'night';
	}

	/**
	 * Checks if a new monologue can be generated based on timing constraints.
	 *
	 * This method enforces the minimum interval between monologue generations to prevent
	 * spam and ensure thoughtful, spaced-out content. It checks the timestamp of the
	 * last generated monologue against the configured minimum interval.
	 *
	 * @param {Object} env - Environment variables containing Redis configuration
	 * @returns {Promise<boolean>} True if enough time has passed since last generation, false otherwise
	 *
	 * @example
	 * const canGenerate = await monologueService.canGenerateMonologue(env);
	 * if (canGenerate) {
	 *   const monologue = await monologueService.generateMonologue(env);
	 * } else {
	 *   console.log('Too soon to generate another monologue');
	 * }
	 */
	async canGenerateMonologue(env) {
		const redis = getRedisClient(env);
		const lastGenerated = await redis.get(REDIS_KEYS.LAST_GENERATED);

		if (!lastGenerated) return true; // First time generation is always allowed

		const timeSinceLastMs = Date.now() - new Date(lastGenerated).getTime();
		return timeSinceLastMs >= CONFIG.MIN_INTERVAL_MS;
	}

	/**
	 * Retrieves comprehensive statistics about monologue generation.
	 *
	 * Gathers all available statistics including generation count, timestamps,
	 * history length, and the ability to generate new monologues. This provides
	 * a complete overview of the monologue service's current state.
	 *
	 * @param {Object} env - Environment variables containing Redis configuration
	 * @returns {Promise<Object>} Statistics object containing monologue generation data
	 * @returns {number} return.totalGenerated - Total number of monologues generated
	 * @returns {string|null} return.lastGenerated - ISO timestamp of last generation
	 * @returns {number|null} return.timeSinceLastMs - Milliseconds since last generation
	 * @returns {number} return.historyLength - Number of monologues in history
	 * @returns {Object} return.lastStats - Detailed stats from last generation
	 * @returns {boolean} return.canGenerateNow - Whether a new monologue can be generated
	 *
	 * @example
	 * const stats = await monologueService.getStats(env);
	 * console.log(`Generated ${stats.totalGenerated} monologues`);
	 * console.log(`Can generate now: ${stats.canGenerateNow}`);
	 */
	async getStats(env) {
		const redis = getRedisClient(env);

		// Fetch all statistics in parallel for efficiency
		const [lastGenerated, count, statsJson, historyLength] = await Promise.all([
			redis.get(REDIS_KEYS.LAST_GENERATED),
			redis.get(REDIS_KEYS.COUNT),
			redis.get(REDIS_KEYS.STATS),
			redis.llen(REDIS_KEYS.HISTORY),
		]);

		return {
			totalGenerated: parseInt(count) || 0,
			lastGenerated,
			timeSinceLastMs: lastGenerated ? Date.now() - new Date(lastGenerated).getTime() : null,
			historyLength: historyLength || 0,
			lastStats: statsJson ? JSON.parse(statsJson) : {},
			canGenerateNow: await this.canGenerateMonologue(env),
		};
	}
}
