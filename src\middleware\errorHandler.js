import { sendTelegramError } from '../chat/telegram/index.js';

/**
 * Hono middleware for centralized error handling.
 *
 * This middleware catches errors thrown by preceding middleware or route handlers,
 * logs them with detailed context, sends error notifications to Telegram (in production),
 * and returns standardized JSON error responses.
 */
export const errorHandlerMiddleware = async (c, next) => {
	try {
		// Attempt to proceed with the request handling chain
		await next();

		// Handle cases where a response might not have been set properly downstream.
		// Hono usually handles 404s automatically, but this provides a fallback.
		if (!c.res.status) {
			console.warn(`No response set for path: ${c.req.path}, returning 404.`);
			// Uncomment to return a standard 404 JSON response:
			// return c.json({ error: true, details: 'Not Found', status: 404 }, 404);
		}
	} catch (err) {
		// Log the error with details for debugging
		console.error('Unhandled error caught in errorHandlerMiddleware:', {
			message: err.message,
			stack: err.stack,
			path: c.req.path,
			method: c.req.method,
			// Note: Be careful with logging sensitive data from headers or body
		});

		// Send error report to Tel<PERSON>ram asynchronously (don't block response)
		// This helps with monitoring and alerting in production
		sendTelegramError(c.env, err, { path: c.req.path, method: c.req.method });

		// Determine the status code - default to 500 for internal server errors
		const statusCode = err.status || err.statusCode || 500;

		// Return a standardized JSON error response
		// Avoid leaking stack traces or sensitive details in production
		const errorResponse = {
			error: true,
			details: err.message || 'An unexpected internal server error occurred.',
			status: statusCode,
		};

		return c.json(errorResponse, statusCode);
	}
};
