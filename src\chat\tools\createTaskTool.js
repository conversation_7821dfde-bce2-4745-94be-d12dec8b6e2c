import { DynamicTool } from '@langchain/core/tools';

import { parseTaskDescription } from './parseInput.js';

/**
 * Create a LangChain DynamicTool to add a task for a user.
 * Single responsibility: construct and return the create_task tool.
 *
 * @param {TaskManager} taskManager
 * @param {string|number} userId
 * @returns {DynamicTool}
 */
export function createTaskTool(taskManager, userId) {
	return new DynamicTool({
		name: 'create_task',
		description:
			"Use this tool to create a new task or to-do item for the user when they express an intent to remember something or to do something in the future. For example, if they say 'I need to buy milk' or 'remind me to call the doctor'. Input should be a JSON object with a 'description' field containing the task description.",
		func: async (input) => {
			try {
				if (!userId) {
					return 'Error: User ID is not available. Cannot create task.';
				}

				const description = parseTaskDescription(input);
				if (!description) {
					console.error('create_task tool: No valid description provided. Input was:', input);
					return 'Error: Task description is required and cannot be empty. Please provide a clear description of the task you want to create.';
				}

				const task = await taskManager.createTask(userId, description);
				console.log(`create_task: Created task ${task.id} for user ${userId}.`);
				return `Successfully created task: "${description}". You should now confirm this with the user.`;
			} catch (error) {
				console.error('Error in create_task tool:', error);
				console.error('Input that caused error:', input);
				return 'Error: Failed to create the task due to an internal error.';
			}
		},
		schema: {
			type: 'object',
			properties: {
				description: {
					type: 'string',
					description:
						'The detailed description of the task to be created. This should be a clear, actionable description of what the user wants to remember or do.',
				},
			},
			required: ['description'],
		},
	});
}
