import { waitUntil } from '@vercel/functions';
import { Hon<PERSON> } from 'hono';

import { MonologueService } from '../chat/MonologueService.js';
import { sendTelegramError, sendTelegramMessage } from '../chat/telegram/index.js';
import { escapeHtml } from '../chat/telegramUtils.js';

const thoughtsRoutes = new Hono();

/**
 * AI Thoughts/Monologue Endpoint
 * Generates an AI monologue and sends it to a Telegram chat
 */
thoughtsRoutes.post('/thoughts', async (c) => {
	try {
		console.log('[Thoughts Endpoint] Received request for AI monologue generation');

		// Initialize the monologue service
		const monologueService = new MonologueService();

		// Check if enough time has passed since last monologue (rate limiting)
		const canGenerate = await monologueService.canGenerateMonologue(c.env);
		if (!canGenerate) {
			console.log('[Thoughts Endpoint] Rate limit: Not enough time since last monologue');

			// Log rate limiting for monitoring
			const rateLimitContext = {
				path: '/thoughts',
				method: 'POST',
				type: 'rate_limit',
				timestamp: new Date().toISOString(),
			};
			console.warn('[Thoughts Endpoint] Rate limit triggered:', rateLimitContext);

			return c.json(
				{
					success: false,
					error: 'Rate limited: Please wait before generating another monologue',
					nextAllowedTime: 'in a few minutes',
					retryAfter: 480, // 8 minutes in seconds
				},
				429,
			);
		}

		// Generate the AI monologue
		console.log('[Thoughts Endpoint] Generating AI monologue...');
		const monologue = await monologueService.generateMonologue(c.env);

		// Validate the generated monologue
		if (!monologue || typeof monologue !== 'string') {
			console.error('[Thoughts Endpoint] Failed to generate valid monologue');
			return c.json(
				{
					success: false,
					error: 'Failed to generate valid monologue',
				},
				500,
			);
		}

		// Send the monologue to the specified Telegram chat
		const targetChatId = '-4693815869'; // Specified chat ID
		console.log(`[Thoughts Endpoint] Sending monologue to chat ${targetChatId}`);

		// Format the monologue text for HTML formatting
		const formattedMonologue = escapeHtml(monologue);
		const telegramResponse = await sendTelegramMessage(c.env, targetChatId, formattedMonologue, { parseMode: 'HTML' });

		// Handle Telegram send failure
		if (!telegramResponse) {
			console.error('[Thoughts Endpoint] Failed to send message to Telegram');

			// Send error notification to admin (async, don't block response)
			const telegramErrorContext = {
				path: '/thoughts',
				method: 'POST',
				type: 'telegram_send_failure',
				chatId: targetChatId,
				monologueLength: monologue?.length || 0,
			};

			// Use waitUntil to ensure error notification is sent
			waitUntil(sendTelegramError(c.env, new Error('Failed to send monologue to Telegram'), telegramErrorContext));

			return c.json(
				{
					success: false,
					error: 'Generated monologue but failed to send to Telegram',
					monologue: monologue, // Include the generated content for debugging
					chatId: targetChatId,
				},
				500,
			);
		}

		console.log('[Thoughts Endpoint] Successfully sent monologue to Telegram');

		// Return success response
		return c.json({
			success: true,
			message: 'AI monologue generated and sent successfully',
			chatId: targetChatId,
			timestamp: new Date().toISOString(),
			telegramMessageId: telegramResponse.result?.message_id,
		});
	} catch (error) {
		console.error('[Thoughts Endpoint] Error:', error);

		// Log error context for debugging
		const errorContext = {
			path: '/thoughts',
			method: 'POST',
			error: error.message,
			stack: error.stack,
			timestamp: new Date().toISOString(),
			chatId: '-4693815869',
		};
		console.error('[Thoughts Endpoint] Error context:', errorContext);

		// Send error notification to admin (async, don't block response)
		waitUntil(
			sendTelegramError(c.env, error, errorContext).catch((notificationError) => {
				console.error('[Thoughts Endpoint] Failed to send error notification:', notificationError);
			}),
		);

		// Determine appropriate status code
		const statusCode = error.status || error.statusCode || 500;
		const isClientError = statusCode >= 400 && statusCode < 500;

		return c.json(
			{
				success: false,
				error: isClientError ? error.message : 'Internal server error during monologue generation',
				timestamp: new Date().toISOString(),
			},
			statusCode,
		);
	}
});

export default thoughtsRoutes;
